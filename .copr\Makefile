# This Makefile script is invoked by copr to build source rpm
# See: https://docs.pagure.org/copr.copr/user_documentation.html#make-srpm
#
# How to test/setup
# 1- login to Copr account
# 2- add a project setting the desired chroots (for testing copy the
#    one on official Copr project)
# 3- add a SCM build
#   a- URL: url of git repository
#   b- Committish: branch name to use, empty master
#   c- build with make_srpm

PROTOCOL_GIT_REPO = https://gitlab.freedesktop.org/spice/spice-protocol
BUILD = xz git rpm-build meson

srpm:
	dnf install -y $(BUILD)

	# get upstream spice protocol
	git clone $(PROTOCOL_GIT_REPO)
	meson --buildtype=release spice-protocol build-spice-protocol --prefix=/usr --werror
	ninja -C build-spice-protocol install

	# get other dependencies for project excluding spice-protocol
	dnf install -y `sed '/^BuildRequires:/!d; s/.*://; s/python36/python3/; s/\bspice-protocol\b//; s/>.*//' *.spec.in`

	# set project version
	sed -i -E "s/^(Release: +).*/\\1$$(date +'%Y%m%d%H%M.spice.latest')/" spice-streaming-agent.spec.in
	git add spice-streaming-agent.spec.in
	git config --local user.email "<EMAIL>"
	git config --local user.name "dummy"
	git commit -m 'automatic version update'

	# create source rpm
	rm -rf build && mkdir build
	git submodule update --init --recursive
	meson . build --prefix=/usr --werror
	if ! test -r ../spice-common.git; then DIR=`basename "$$PWD"`; ln -s "$$DIR/.git/modules/spice-common" ../spice-common.git; fi
	ninja -C build dist
	rpmbuild -ts build/meson-dist/spice-streaming-agent-*.tar.xz --define "_srcrpmdir $(outdir)"
