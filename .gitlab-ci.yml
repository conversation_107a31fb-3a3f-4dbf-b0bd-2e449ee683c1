image: fedora:latest

variables:
  GIT_SUBMODULE_STRATEGY: recursive

before_script:
    - dnf install -y git xz rpmlint findutils
    # get dependencies from spec file
    - dnf install -y `sed '/^BuildRequires:/!d; s/.*://; s/python36/python3/; s/\bspice-protocol\b//; s/>.*//' spice-streaming-agent.spec.in`
    - git clone ${CI_REPOSITORY_URL/spice-streaming-agent/spice-protocol}
    - meson --buildtype=release spice-protocol build-spice-protocol --prefix=/usr --werror
    - ninja -C build-spice-protocol install

build_and_test:
  script:
    - meson --buildtype=release --werror build-default
    # check SPEC file
    - rpmlint -o "NetworkEnabled False" build-default/spice-streaming-agent.spec
    # this fix an issue with Meson dist
    - if ! test -r ../spice-common.git; then DIR=`basename "$PWD"`; ln -s "$DIR/.git/modules/spice-common" ../spice-common.git; fi
    - ninja -C build-default dist
    - ninja -C build-default
    - ninja -C build-default test

    - meson --buildtype=release --werror build-feat-disabled -Dauto_features=disabled
    - ninja -C build-feat-disabled
    - ninja -C build-feat-disabled test
