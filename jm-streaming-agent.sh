#!/bin/sh

# this the boot script of jm-streaming-agent service. it does a lot of initializations
# before running the main proccess.
# 
# the main proccess runs in background and a infinite loop is used to check for various 
# of conditions on which the proccess should be restarted.


# detect_active_user()'s outputs params
Active_User=""
Active_Display=""
# Function: find the active user and active display that logined in through x11 session. 
detect_active_user() {
	for i_sessionid in $(loginctl list-sessions --no-legend | awk '{ print $1}')
	do
		i_state=`loginctl show-session -p State  $i_sessionid`
		#echo "in loop: "$i_state
		if test $i_state = "State=active"
		then
			i_type=`loginctl show-session -p Type  $i_sessionid`
			#echo "in loop: "$i_type
			if test $i_type = "Type=x11"
			then
				i_name=`loginctl show-session -p Name  $i_sessionid`
				Active_User=${i_name#*=}
				i_display=`loginctl show-session -p Display  $i_sessionid`
				Active_Display=${i_display#*=}
				#echo "in loop: "$Active_User
				#echo "in loop: "$Active_Display
				break
			fi
		fi
	done
}


# detect_cards()'s outputs params
Jmgpu_Dri_Node=""
# function: find the jmgpu card
detect_cards() {
	for i in $(lspci -nn -d 0731:* | grep -E "92|11|96" | awk '{ print $1 }'); 
	do 
		jmgpu_drm_path=/dev/dri/by-path/$(ls -l /dev/dri/by-path | grep $i | awk ' {print $11}' | grep card);
		jmgpu_drm_path=$(realpath $jmgpu_drm_path);
		Jmgpu_Dri_Node=$jmgpu_drm_path		
		#echo [card:$i,node:$jmgpu_drm_path]; 
	done
}

# detect_architecture()'s outputs params
Host_Architecture=""
# function: dectect architecture
detect_architecture() {
	OS=$(cat /etc/os-release | sed -n '/^ID=/p' | sed -e 's/"//g' -e 's/ID=//g')
	# aarch64-linux-gnu : /usr/lib/aarch64-linux-gnu
	# mips64el-linux-gnuabi64 : /usr/lib/mips64el-linux-gnuabi64
	# x86_64-linux-gnu : /usr/lib/x86_64-linux-gnu
	# mips64el-linux-gnu : /usr/lib/mips64el-linux-gnu
	Host_Architecture=`dpkg-architecture -qDEB_HOST_MULTIARCH`
	if test $OS = "nfsdesktop"
	then
		Host_Architecture=`uname -m`-linux-gnu
	fi
}

# shell script input params, which screen to be captured
Screen_Index=$1

echo "jm-streaming-agent-@$Screen_Index starting to run"
# call the function
detect_active_user
echo "active user "$Active_User ", active Display "$Active_Display

# call detect_cards
detect_cards
#Jmgpu_Dri_Node=/dev/dri/card0
echo "jmgpu card node "$Jmgpu_Dri_Node

# call detect_architecture
detect_architecture
echo "host architecture "$Host_Architecture

# on kylin, a proccess must have 'cap_sys_admin' capability to access drm
setcap cap_sys_admin+ep /usr/local/bin/jm-streaming-agent

# allow any clients connecting to xserver
DISPLAY=$Active_Display xhost +
if [ $? -ne 0 ]; then
	# if a user logout from client and lightdm restarts, the key of current user would not update. 'xauth' is for that   
	xauth -v add `xauth -f /var/run/lightdm/root/$Active_Display list`
	DISPLAY=$Active_Display xhost +
	echo "retry xauth"
	if [ $? -ne 0 ]; then
		echo "xhost can not access X server"
		echo "to restart shell script"
		exit 0
	else
		echo "xhost success"	
	fi
else
	echo "xhost success"	
fi

# make sure the access mode is 666 
virtio_stream_port=/dev/virtio-ports/org.spice-space.stream.$Screen_Index
chmod 666 $virtio_stream_port

# jmsdk path
Jmsdk_Path=/usr/lib/$Host_Architecture/jmfbc/libjmfbc.so

# rate-control 0:cqp 1:cbr  2:vbr  3:cvbr 4:qvbr 5:qpmap
# start the service, runs it in background with '&'
export DISPLAY=$Active_Display 
export LIBVA_DRIVER_NAME=mwv207d 
# export GST_VAAPI_ALL_DRIVERS=1
# export GST_DEBUG=3
export JMFBC_PP=0
export JMFBC_FPSI=59
export JMFBC_STATISTIC=1
export JMFBC_ADAPTER=$Jmgpu_Dri_Node
# export JMFBC_ASYNC_DAMAGE=0

	/usr/local/bin/jm-streaming-agent \
	-p $virtio_stream_port \
	-c screen-id=$Screen_Index \
	-c plugin-name=jmfbc \
	-c jmfbc-path=$Jmsdk_Path \
	-c usevblank=0 \
	-c framerate=60/1 \
	-c capx=0 \
	-c capy=0 \
	-c capw=0 \
	-c caph=0 \
	-c capture-mode=0 \
	-c diffmap=1 \
	-c codec=0 \
	-c profile=baseline \
	-c rate-control=4 \
	-c peak-bitrate=15000000 \
	-c bitrate=6000000 \
	-c init-qp=26 \
	-c min-qp=0 \
	-c max-qp=51 \
	-c i-qp=26 \
	-c p-qp=26 \
	-c b-qp=26 \
	-c quality-level=1 \
	-c keyframe-period=300 \
	-c i-period=60 \
	-c ip-period=1 \
	-c watermark=0 \
	-c watermark-text="JINGJIA MICRO 2024" \
	-c fpsi=59 \
	-c rss-limit=-10 \
	-c user-name=$Active_User \
	-c cap-fail-cnt=1000000 \
	-c bomb-timeout=20000 \
	-c draw-cursor=0 \
	-d &

# -c debug-output=/tmp \
# -c gst.h264=vaapih264enc \
# -c output-name="" \
# -c capture=kms \
# -c usedamage=1 \
# -c buildin-pp=1 \
# -c device=$Jmgpu_Dri_Node \
# -c force-streaming=1 \

Pid="$!"
echo "jm-streaming-agent-@$Screen_Index pid "$Pid

# save current active user & active display
Cur_Active_User=$Active_User
Cur_Active_Display=$Active_Display

# blocking in this loop, to check if exiting this service
while true
do 
	sleep 0.5

	# check active user
	detect_active_user
	if test $Cur_Active_User != $Active_User
	then
		echo "active user changed from "$Cur_Active_User" to "$Active_User
		echo "to restart shell script"
		break
	else
		if test $Cur_Active_Display != $Active_Display
		then
			echo "active display changed from "$Cur_Active_Display" to "$Active_Display
			echo "to restart shell script"
			break
		fi
	fi

	# check main proccess status
        cur_pid=`ps aux | grep "jm-streaming-agent" | awk '{ print $2}' | grep $Pid`
	
	if test -z "$cur_pid"
	then
		echo "main proccess exited, to restart shell script"
		break;
	fi

	if test $cur_pid != $Pid
	then
		echo "main proccess exited, to restart shell script"
		break
	fi

	# other checks 
	# ...
done

echo "jm-streaming-agent-@$Screen_Index shell script exit"
