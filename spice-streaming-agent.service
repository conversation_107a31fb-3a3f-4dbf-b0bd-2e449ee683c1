[Unit]
Description=jm-streaming-agent-service
Documentation=
Conflicts=
After=lightdm.service
Requires=lightdm.service

[Service]
Type=simple
Environment="DISPLAY=:0"
Environment="LIBVA_DRIVER_NAME=jmgpu"
Environment="GST_VAAPI_ALL_DRIVERS=1"
# the password of 'root'
Environment="ROOT_PSW=passwordis123"
Environment="GST_DEBUG=3"

# on kylin, a proccess must have 'cap_sys_admin' capability to access drm
ExecStartPre=/bin/sh -c "echo $ROOT_PSW | sudo -S setcap cap_sys_admin+ep /usr/local/bin/jm-streaming-agent"
# if a user logout from client and lightdm restarts, the key of jingjia would not update. 'xauth' is for that   
ExecStartPre=/bin/sh -c "xauth add `echo $ROOT_PSW | sudo -S xauth -f /var/run/lightdm/root/:0 list`"
# sometimes, jing<PERSON><PERSON> can not access this node ?
ExecStartPre=/bin/sh -c "echo $ROOT_PSW | sudo -S chmod 666 /dev/dri/card1"
ExecStartPre=/bin/sh -c "xhost +"
ExecStart=/usr/local/bin/jm-streaming-agent -c gst.h264=vaapih264enc -c framerate=60/1 -c capture=kms -c capture-device=/dev/dri/card1 -c capture-display=:0 -c usedamage=1 -c capx=0 -c capy=0 -c capw=0 -c caph=0 -c usevblank=1 -c rss-limit=-10 -c sdk-path=/usr/local/lib/aarch64-linux-gnu/jmfbc -c sdk-name=libjmfbc.so -c plugin-name=gst -c fpsi=300 -c screen-id=0 -d
Restart=always
User=nameisjingjia

[Install]
WantedBy=multi-user.target
