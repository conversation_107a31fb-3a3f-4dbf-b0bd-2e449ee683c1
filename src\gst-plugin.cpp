/* Plugin implementation for gstreamer encoder
 *
 * \copyright
 * Copyright 2018 Red Hat Inc. All rights reserved.
 */

#include <config.h>
#include <cstring>
#include <sstream>
#include <exception>
#include <stdexcept>
#include <memory>
#include <map>
#include <syslog.h>
#include <unistd.h>
#include <gst/gst.h>
#include <gst/app/gstappsink.h>

#define XLIB_CAPTURE 0 /* use gst plugin, not xlib api */
#if XLIB_CAPTURE
#include <gst/app/gstappsrc.h>
#endif

#include <spice-streaming-agent/plugin.hpp>
#include <spice-streaming-agent/frame-capture.hpp>
#include <spice-streaming-agent/x11-display-info.hpp>

#define GstPlugin_Name "gst"
#define gst_syslog(priority, str, ...) syslog(priority, "Gstreamer plugin: " str, ## __VA_ARGS__);

namespace spice {
namespace streaming_agent {
namespace gstreamer_plugin {

struct GstreamerEncoderSettings
{
    int fps = 30;
    SpiceVideoCodecType codec = SPICE_VIDEO_CODEC_TYPE_VP8;
    std::string encoder;
    std::map<std::string, std::string> enc_props;
    gboolean use_damage = FALSE;
	gboolean buildin_pp = TRUE;
    gboolean wait_damage = FALSE;
    std::string capture_plugin_name;  /* kms or ximagesrc */
    std::string capture_device_name;  /* drm device name */
    std::string capture_display_name = ":0"; /* for kms xdamage */
    gboolean use_vblank = FALSE;
    guint rate_control = 1;
    guint quality_level = 4;
    guint init_qp = 26;
    guint min_qp = 1;
	guint max_qp = 51;
    gint qp_ip = 0;
    gint bitrate = 0;
    gint screen_id = 0;
	gint keyframe_period = 30;
	gboolean cabac = FALSE;
	gboolean dct8x8 = FALSE;
	gint max_bframes = 0;
	gint refs_num = 1;
	gint cpb_length = 1500;
	gint tune = 0;
	gint num_slices = 1;
	std::string user_name = "";
	gint draw_cursor = 2; /* 0: never, 1: draw, 2: only draw while vdagent is on*/

    /* the sub region of drm framebuffer to be captured */
    guint capx = 0;
    guint capy = 0;
    guint capw = 0;
    guint caph = 0;
};

#define DECLARE_UPTR(type, func) \
    struct type##Deleter {       \
        void operator()(type* p) \
        {                        \
            func(p);             \
        }                        \
    };                           \
    using type##UPtr = std::unique_ptr<type, type##Deleter>;

DECLARE_UPTR(GstBuffer, gst_buffer_unref)
DECLARE_UPTR(GstCaps, gst_caps_unref)
DECLARE_UPTR(GstSample, gst_sample_unref)
DECLARE_UPTR(GstElement, gst_object_unref)

class GstreamerFrameCapture final : public FrameCapture
{
public:
    GstreamerFrameCapture(const GstreamerEncoderSettings &settings);
    ~GstreamerFrameCapture();
    FrameInfo CaptureFrame() override;
    void Reset() override;
    SpiceVideoCodecType VideoCodecType() const override {
        return settings.codec;
    }
    std::vector<DeviceDisplayInfo> get_device_display_info() const override;
private:
    void free_sample();
    GstElement *get_encoder_plugin(const GstreamerEncoderSettings &settings, GstCapsUPtr &sink_caps);
    GstElement *get_capture_plugin(const GstreamerEncoderSettings &settings);
    void pipeline_init(const GstreamerEncoderSettings &settings);
    Display *const dpy;
#if XLIB_CAPTURE
    void xlib_capture();
#endif
    GstElementUPtr pipeline, capture, sink;
    GstSampleUPtr sample;
    GstMapInfo map = {};
    uint32_t last_width = ~0u, last_height = ~0u;
    uint32_t cur_width = 0, cur_height = 0;
    bool is_first = true;
    GstreamerEncoderSettings settings; // will be set by plugin settings
    GThread *vdagent_thread = NULL;
};

class GstreamerPlugin final: public Plugin
{
public:
    FrameCapture *CreateCapture() override;
    unsigned Rank() override;
	std::string Name()  override;
    void ParseOptions(const ConfigureOption *options, const std::string &codec_name,
                      const std::string &encoder_cfg);
    SpiceVideoCodecType VideoCodecType() const override {
        return settings.codec;
    }
private:
    void StoreEncodingOptions(const std::string &encoder_options);
    bool StorePluginOption(const std::string &name, const std::string &value);
    GstreamerEncoderSettings settings;
};


static char vdagent_check_cmds[128] = {0};


static int
vdagent_check(gpointer user_data)
{
	GstElement *inst  = (GstElement *)user_data;

	if (!G_IS_OBJECT (inst)) {
		return -1;
	}

	char ret[128] = {0};
	FILE* fp = popen(vdagent_check_cmds, "r");
	fread(ret, 1, 128, fp);
	fclose(fp);
	
	if (strlen(ret) <= 0) {
		g_object_set(inst, "draw-cursor", TRUE, nullptr);
		syslog(LOG_DEBUG, "draw cursor");
	}
	else {
		g_object_set(inst, "draw-cursor", FALSE, nullptr);
		syslog(LOG_DEBUG, "not draw cursor");
	}

	return 0;
}

static gpointer
vdagent_check_thread(gpointer user_data)
{
	syslog(LOG_DEBUG, "%s start", __func__);
	static uint32_t check_num = 0;
	static uint32_t check_num_max = 120;
	
	while (check_num < check_num_max) {   // why we need so long a time to check it? as kylin GFB start user service extremely slow.
		if (vdagent_check(user_data) != 0) {
			break;
		}
		check_num++;
		sleep(1);
		syslog(LOG_DEBUG, "vdagent check_num %u < %u", check_num, check_num_max);
	}
	check_num = 0;
	syslog(LOG_DEBUG, "%s exit", __func__);
	return NULL;
}

GstElement *GstreamerFrameCapture::get_capture_plugin(const GstreamerEncoderSettings &settings)
{
    GstElement *capture = nullptr;

#if XLIB_CAPTURE
    capture = gst_element_factory_make("appsrc", "capture");
#else
    if (settings.capture_plugin_name == "x11") {
        capture = gst_element_factory_make("ximagesrc", "capture");
        g_object_set(capture,
                        "use-damage", settings.use_damage,
                        nullptr);
		syslog(LOG_DEBUG, "using ximagesrc");
    } else if (settings.capture_plugin_name == "kms") {
        capture = gst_element_factory_make("jmgpusrc", "capture");
        g_object_set(capture,
                        "use-damage", settings.use_damage,
                        nullptr);
        g_object_set(capture,
                        "wait-vblank", settings.use_vblank,
                        nullptr);
        g_object_set(capture,
                        "framerate", settings.fps,
                        nullptr);
        g_object_set(capture,
                        "device-name", settings.capture_device_name.c_str(),
                        nullptr);
        g_object_set(capture,
                        "display-name", settings.capture_display_name.c_str(),
                        nullptr);
        g_object_set(capture,
                        "x", settings.capx,
                        nullptr);
        g_object_set(capture,
                        "y", settings.capy,
                        nullptr);
        g_object_set(capture,
                        "w", settings.capw,
                        nullptr);
        g_object_set(capture,
                        "h", settings.caph,
                        nullptr);
        g_object_set(capture,
                        "screen", settings.screen_id,
                        nullptr);

        if (settings.draw_cursor == 2) {
            vdagent_check(capture);
            vdagent_thread = g_thread_new("adgent-check-thread", (GThreadFunc) vdagent_check_thread, capture);
        }
        else if (settings.draw_cursor == 0) {
            g_object_set(capture, "draw-cursor", FALSE, nullptr);
        }
		else if (settings.draw_cursor == 1) {
			g_object_set(capture, "draw-cursor", TRUE, nullptr);
		}

		syslog(LOG_DEBUG, "using jmgpusrc");
	}

#endif
    return capture;
}

GstElement *GstreamerFrameCapture::get_encoder_plugin(const GstreamerEncoderSettings &settings,
                                                      GstCapsUPtr &sink_caps)
{
    GList *encoders;
    GList *filtered;
    GstElement *encoder;
    GstElementFactory *factory = nullptr;

    switch (settings.codec) {
    case SPICE_VIDEO_CODEC_TYPE_H264:
        sink_caps.reset(gst_caps_new_simple("video/x-h264",
                                            "stream-format", G_TYPE_STRING, "byte-stream",
                                            nullptr));
        break;
    case SPICE_VIDEO_CODEC_TYPE_MJPEG:
        sink_caps.reset(gst_caps_new_empty_simple("image/jpeg"));
        break;
    case SPICE_VIDEO_CODEC_TYPE_VP8:
        sink_caps.reset(gst_caps_new_empty_simple("video/x-vp8"));
        break;
    case SPICE_VIDEO_CODEC_TYPE_VP9:
        sink_caps.reset(gst_caps_new_empty_simple("video/x-vp9"));
        break;
    case SPICE_VIDEO_CODEC_TYPE_H265:
        sink_caps.reset(gst_caps_new_empty_simple("video/x-h265"));
        break;
    default : /* Should not happen - just to avoid compiler's complaint */
        throw std::logic_error("Unknown codec");
    }
    gst_caps_set_simple(sink_caps.get(), "framerate", GST_TYPE_FRACTION, settings.fps, 1, nullptr);
    std::unique_ptr<gchar, decltype(&g_free)> caps_str(gst_caps_to_string(sink_caps.get()), g_free);

    encoders = gst_element_factory_list_get_elements(GST_ELEMENT_FACTORY_TYPE_VIDEO_ENCODER, GST_RANK_NONE);
    filtered = gst_element_factory_list_filter(encoders, sink_caps.get(), GST_PAD_SRC, false);
    if (filtered) {
        gst_syslog(LOG_NOTICE, "Looking for encoder plugins which can produce a '%s' stream", caps_str.get());
        for (GList *l = filtered; l != nullptr; l = l->next) {
            if (!factory && !settings.encoder.compare(GST_ELEMENT_NAME(l->data))) {
                factory = (GstElementFactory*)l->data;
            }
            gst_syslog(LOG_NOTICE, "'%s' plugin is available", GST_ELEMENT_NAME(l->data));
        }
        if (!factory && !settings.encoder.empty()) {
            gst_syslog(LOG_WARNING,
                       "Specified encoder named '%s' cannot produce '%s' streams. Make sure that "
                       "gst.CODEC=ENCODER is correctly specified and that the encoder is available.",
                       settings.encoder.c_str(), caps_str.get());
        }
        factory = factory ? factory : (GstElementFactory*)filtered->data;
        gst_syslog(LOG_NOTICE, "'%s' encoder plugin is used", GST_ELEMENT_NAME(factory));

    } else {
        gst_syslog(LOG_ERR, "No suitable encoder was found for '%s'", caps_str.get());
    }

    encoder = factory ? gst_element_factory_create(factory, "encoder") : nullptr;
    if (encoder) { // Set encoder properties
        if(strcmp(GST_ELEMENT_NAME(factory) ,"vaapih264enc") == 0 || strcmp(GST_ELEMENT_NAME(factory) ,"vaapih265enc") == 0) {
	        gst_syslog(LOG_ERR, "bitrate %d, rate-control %d, quality-level %d, " \ 
				                "init_qp %d, min_qp %d, max_qp %d, qp_ip %d, keyframe_period %d, " \ 
				                "num-slices %d, refs = %d, max-bframes = %d, cabac = %d, dct8x8 = %d, "\
				                "cpb-length = %d, tune = %d",
	                    settings.bitrate, settings.rate_control, settings.quality_level, 
	                    settings.init_qp, settings.min_qp, settings.max_qp, settings.qp_ip, 
	                    settings.keyframe_period, settings.num_slices, settings.refs_num, 
	                    settings.max_bframes, settings.cabac, settings.dct8x8, settings.cpb_length,
	                    settings.tune);
	        g_object_set(encoder,
				"bitrate", settings.bitrate,
				"rate-control", settings.rate_control,
				"quality-level", settings.quality_level,
				"init-qp", settings.init_qp,
				"min-qp", settings.min_qp,
				"max-qp", settings.max_qp,
				"qp-ip", settings.qp_ip,
				"keyframe-period", settings.keyframe_period,
				"num-slices", settings.num_slices,
				"refs", settings.refs_num,
				"max-bframes", settings.max_bframes,
				"cabac", settings.cabac,
				"dct8x8", settings.dct8x8,
				"cpb-length", settings.cpb_length,
				"tune", settings.tune,
				NULL);
        }
		for (const auto &prop : settings.enc_props) {
            const auto &name = prop.first;
            const auto &value = prop.second;
            if (!g_object_class_find_property(G_OBJECT_GET_CLASS(encoder), name.c_str())) {
                gst_syslog(LOG_WARNING, "'%s' property was not found for this encoder",
                           name.c_str());
                continue;
            }
            gst_syslog(LOG_NOTICE, "Trying to set encoder property: '%s = %s'",
                       name.c_str(), value.c_str());
            /* Invalid properties will be ignored silently */
            gst_util_set_object_arg(G_OBJECT(encoder), name.c_str(), value.c_str());
        }
    }
    gst_plugin_feature_list_free(filtered);
    gst_plugin_feature_list_free(encoders);
    return encoder;
}

// Utility to add an element to a GstBin
// This checks return value and update reference correctly
void gst_bin_add(GstBin *bin, const GstElementUPtr &elem)
{
    if (::gst_bin_add(bin, elem.get())) {
        // ::gst_bin_add take ownership using floating references but
        // we still hold a reference in elem so update the reference
        // accordingly
        g_object_ref(elem.get());
    } else {
        throw std::runtime_error("Gstreamer's element cannot be added to pipeline");
    }
}

static gboolean gst_wait_for_state(GstElement* element, GstState state)
{
    gboolean ret = TRUE;
    while (TRUE) {
        GstStateChangeReturn sret;
        GstState nowstate;
        sret = gst_element_get_state (element, &nowstate, NULL, 100 * GST_MSECOND);
        if (sret == GST_STATE_CHANGE_SUCCESS || sret == GST_STATE_CHANGE_NO_PREROLL) {
            if (nowstate == state) {
                syslog(LOG_DEBUG, "[%s] success", __func__);
            } else {
                syslog(LOG_DEBUG, "[%s] preroll success", __func__);
            }
            break;
        } else if (sret == GST_STATE_CHANGE_ASYNC) {
            continue;
        } else {
            syslog(LOG_ERR, "[%s] fail", __func__);
            ret = FALSE;
            break;
        }
    }

    return ret;
}

void GstreamerFrameCapture::pipeline_init(const GstreamerEncoderSettings &settings)
{
    gboolean link;
    GstElementUPtr pipeline(gst_pipeline_new("pipeline"));
    if (!pipeline) {
        throw std::runtime_error("Gstreamer's pipeline element cannot be created");
    }
    GstElementUPtr capture(get_capture_plugin(settings));
    if (!capture) {
        throw std::runtime_error("Gstreamer's capture element cannot be created");
    }

    GstElementUPtr convert;
    if (settings.capture_plugin_name == "x11") {
        GstElement* convert_ = gst_element_factory_make("capsfilter", "capsfilter");
        if (!convert_) {
            throw std::runtime_error("Gstreamer's 'capsfilter' element cannot be created");
        }
        convert.reset(convert_);
    }
    else if (settings.capture_plugin_name == "kms" && !settings.buildin_pp) {
        GstElement* convert_ = gst_element_factory_make("vaapipostproc", "vaapipostproc");
        if (!convert_) {
            throw std::runtime_error("Gstreamer's 'vaapipostproc' element cannot be created");
        }
        convert.reset(convert_);
    }

    GstCapsUPtr sink_caps;
    GstElementUPtr encoder(get_encoder_plugin(settings, sink_caps));
    if (!encoder) {
        throw std::runtime_error("Gstreamer's encoder element cannot be created");
    }

    GstElementUPtr sink(gst_element_factory_make("appsink", "sink"));
    if (!sink) {
        throw std::runtime_error("Gstreamer's appsink element cannot be created");
    }

    g_object_set(sink.get(),
                 "sync", FALSE,
                 "drop", FALSE,
                 "max-buffers", 1,
                 nullptr);

    GstBin *bin = GST_BIN(pipeline.get());
    gst_bin_add(bin, capture);
    if (settings.capture_plugin_name == "x11" ||  
		(settings.capture_plugin_name == "kms" && !settings.buildin_pp)) {
        gst_bin_add(bin, convert);
    }
    gst_bin_add(bin, encoder);
    gst_bin_add(bin, sink);

    if (settings.capture_plugin_name == "kms") {
		if (settings.buildin_pp) {
        	link = gst_element_link_many(capture.get(), encoder.get(), sink.get(), NULL);
		}
		else {
			link = gst_element_link_many(capture.get(), convert.get(), encoder.get(), sink.get(), NULL);
		}
    } else if (settings.capture_plugin_name == "x11") {
        link = gst_element_link_many(capture.get(), convert.get(), encoder.get(), sink.get(), NULL);
		GstCapsUPtr filter_caps(gst_caps_new_simple("video/x-raw", "framerate", GST_TYPE_FRACTION, settings.fps, 1, nullptr));
		g_object_set(convert.get(), "caps", filter_caps.get(), NULL);
    }
    if (!link) {
        throw std::runtime_error("Linking gstreamer's elements failed");
    }

    gst_element_set_state(pipeline.get(), GST_STATE_PLAYING);
    gst_wait_for_state(pipeline.get(), GST_STATE_PLAYING);
    GST_DEBUG_BIN_TO_DOT_FILE(bin, GST_DEBUG_GRAPH_SHOW_VERBOSE, "gst-plugin-pipeline-debug");

    this->sink.swap(sink);
    this->capture.swap(capture);
    this->pipeline.swap(pipeline);
}

GstreamerFrameCapture::GstreamerFrameCapture(const GstreamerEncoderSettings &settings):
    dpy(XOpenDisplay(nullptr)),settings(settings)
{
    if (!dpy) {
        throw std::runtime_error("Unable to initialize X11");
    }

    pipeline_init(settings);
}

void GstreamerFrameCapture::free_sample()
{
    if (sample) {
        gst_buffer_unmap(gst_sample_get_buffer(sample.get()), &map);
        sample.reset();
    }
}

GstreamerFrameCapture::~GstreamerFrameCapture()
{
    syslog(LOG_DEBUG, "releasing GstreamerFrameCapture");

    free_sample();

    /*
        while resolution changing, GstreamerFrameCapture object would be released and created again.
        we must set gst pipeline state to NULL before create a new one.
    */
    gst_element_set_state(pipeline.get(), GST_STATE_NULL);
    gst_wait_for_state(pipeline.get(), GST_STATE_NULL);

    syslog(LOG_DEBUG, "releasing GstreamerFrameCapture finished");
    XCloseDisplay(dpy);
}

void GstreamerFrameCapture::Reset()
{
    //TODO
}

#if XLIB_CAPTURE
void free_ximage(gpointer data)
{
    XImage *image = (XImage*)data;
    image->f.destroy_image(image);
}

void GstreamerFrameCapture::xlib_capture()
{
    int screen = XDefaultScreen(dpy);

    Window win = RootWindow(dpy, screen);
    XWindowAttributes win_info;
    XGetWindowAttributes(dpy, win, &win_info);

    /* Some encoders cannot handle odd resolution make sure it's even number of pixels */
    cur_width = win_info.width - win_info.width % 2;
    cur_height =  win_info.height - win_info.height % 2;

    if (cur_width != last_width || cur_height != last_height) {
        last_width = cur_width;
        last_height = cur_height;
        is_first = true;

        gst_app_src_end_of_stream(GST_APP_SRC(capture.get()));
        gst_element_set_state(pipeline.get(), GST_STATE_NULL);//maybe ximagesrc needs eos as well
        gst_element_set_state(pipeline.get(), GST_STATE_PLAYING);
    }

    XImage *image = XGetImage(dpy, win, 0, 0,
                              cur_width, cur_height, AllPlanes, ZPixmap);
    if (!image) {
        throw std::runtime_error("Cannot capture from X");
    }

    GstBufferUPtr buf(gst_buffer_new_wrapped_full(GST_MEMORY_FLAG_PHYSICALLY_CONTIGUOUS, image->data,
                                                  image->height * image->bytes_per_line, 0,
                                                  image->height * image->bytes_per_line, image,
                                                  free_ximage));
    if (!buf) {
        throw std::runtime_error("Failed to wrap image in gstreamer buffer");
    }

    GstCapsUPtr caps(gst_caps_new_simple("video/x-raw",
                                         "format", G_TYPE_STRING, "BGRx",
                                         "width", G_TYPE_INT, image->width,
                                         "height", G_TYPE_INT, image->height,
                                         "framerate", GST_TYPE_FRACTION, settings.fps, 1,
                                         nullptr));

    // Push sample (gst_app_src_push_sample does not take buffer ownership)
    GstSampleUPtr appsrc_sample(gst_sample_new(buf.get(), caps.get(), nullptr, nullptr));
    if (gst_app_src_push_sample(GST_APP_SRC(capture.get()), appsrc_sample.get()) != GST_FLOW_OK) {
        throw std::runtime_error("gstramer appsrc element cannot push sample");
    }
}
#endif

FrameInfo GstreamerFrameCapture::CaptureFrame()
{
    FrameInfo info;

    free_sample(); // free prev if exist

#if XLIB_CAPTURE
    xlib_capture();
#endif
    info.size.width = cur_width;
    info.size.height = cur_height;
    info.stream_start = is_first;
    if (is_first) {
        is_first = false;
    }

    // Pull sample
    GstSample *tmpSample = gst_app_sink_try_pull_sample(GST_APP_SINK(sink.get()), 500 * GST_MSECOND);
    if (tmpSample == NULL) {
        info.buffer = NULL;
        info.buffer_size = 0;
        // syslog(LOG_DEBUG, "gst_app_sink_try_pull_sample timeout");
        return info;
    }
    sample.reset(tmpSample);

    // not use block mode. if resolution changes, the main thread may block on this.
    // sample.reset(gst_app_sink_pull_sample(GST_APP_SINK(sink.get()))); // blocking

    if (sample) { // map after pipeline

        gint width = -1;
        gint height = -1;
        GstStructure *s = gst_caps_get_structure (gst_sample_get_caps(sample.get()), 0);
        if (s && gst_structure_get_int (s, "width", &width) && gst_structure_get_int (s, "height", &height)) {
            if (width != -1 && height != -1 && (width != cur_width || height != cur_height)) {
                cur_width = width;
                cur_height = height;
                info.stream_start = TRUE;
                syslog(LOG_DEBUG, "frame size %dx%d, stream start", cur_width, cur_height);
            }
        }

        info.size.width = cur_width;
        info.size.height = cur_height;

        if (!gst_buffer_map(gst_sample_get_buffer(sample.get()), &map, GST_MAP_READ)) {
            free_sample();
            throw std::runtime_error("Buffer mapping failed");
        }

        info.buffer = map.data;
        info.buffer_size = map.size;
    } else {
        throw std::runtime_error("No sample- EOS or state change");
    }

    return info;
}

std::vector<DeviceDisplayInfo> GstreamerFrameCapture::get_device_display_info() const
{
    try {
        return get_device_display_info_drm(dpy);
    } catch (const std::exception &e) {
        syslog(LOG_WARNING, "Failed to get device info using DRM: %s. Using no-DRM fallback.",
               e.what());
        return get_device_display_info_no_drm(dpy);
    }
}

FrameCapture *GstreamerPlugin::CreateCapture()
{
    return new GstreamerFrameCapture(settings);
}

unsigned GstreamerPlugin::Rank()
{
    return SpecificHardwareMin;
}

std::string GstreamerPlugin::Name() 
{
	return GstPlugin_Name;
}

bool GstreamerPlugin::StorePluginOption(const std::string &name, const std::string &value)
{
    syslog(LOG_DEBUG, "[%s] %s = %s", __func__, name.c_str(), value.c_str());

    if (name == "framerate") {
        try {
            settings.fps = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'framerate'.");
        }
    } else if (name == "usedamage") {
        try {
            settings.use_damage = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'usedamage'.");
        }
    }  else if (name == "buildin-pp") {
        try {
            settings.buildin_pp = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'buildin-pp'.");
        }
    } else if (name == "waitdamage") {
        try {
            settings.wait_damage = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'waitdamage'.");
        }
    }  else if (name == "usevblank") {
        try {
            settings.use_vblank = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'usevblank'.");
        }
    } else if (name == "bitrate") {
        try {
            settings.bitrate = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'bitrate'.");
        }
    } else if (name == "rate-control") {
        try {
            settings.rate_control = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'rate-control'.");
        }
    } else if (name == "quality-level") {
        try {
            settings.quality_level = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'quality-level'.");
        }
    } else if (name == "init-qp") {
        try {
            settings.init_qp = std::stoi(value);
             return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'init-qp'.");
        }
    } else if (name == "screen-id") {
        try {
            settings.screen_id = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'screen-id'.");
        }
    } else if (name == "min-qp") {
        try {
            settings.min_qp = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'min-qp'.");
        }
    } else if (name == "max-qp") {
        try {
            settings.max_qp = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'max-qp'.");
        }
    } else if (name == "keyframe-period") {
        try {
            settings.keyframe_period = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'keyframe-period'.");
        }
    }
	else if (name == "qp-ip") {
        try {
            settings.qp_ip = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'qp_ip'.");
        }
    } else if (name == "capture") {
        try {
            settings.capture_plugin_name = value;
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'capture'.");
        }
    } else if (name == "device") {
        try {
            settings.capture_device_name = value;
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'device'.");
        }
    } else if (name == "capture-display") {
        try {
            settings.capture_display_name = value;
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'capture-display'.");
        }
    } else if (name == "capx") {
        try {
            settings.capx = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'capx'.");
        }
    } else if (name == "capy") {
        try {
            settings.capy = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'capy'.");
        }
    } else if (name == "capw") {
        try {
            settings.capw = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'capw'.");
        }
    } else if (name == "caph") {
        try {
            settings.caph = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'caph'.");
        }
    } else if (name == "cabac") {
        try {
            settings.cabac = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'cabac'.");
        }
    } else if (name == "dct8x8") {
        try {
            settings.dct8x8 = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'dct8x8'.");
        }
    } else if (name == "max-bframes") {
        try {
            settings.max_bframes = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'max-bframes'.");
        }
    } else if (name == "refs") {
        try {
            settings.refs_num = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'refs'.");
        }
    } else if (name == "cpb-length") {
        try {
            settings.cpb_length = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'cpb-length'.");
        }
    } else if (name == "tune") {
        try {
            settings.tune = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'tune'.");
        }
    } else if (name == "num-slices") {
        try {
            settings.num_slices = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'num-slices'.");
        }
    } else if (name == "user-name") {
        try {
            settings.user_name = value;
			sprintf(vdagent_check_cmds, "ps aux | grep -E \"%s .*spice-vdagent\" | grep -v grep", settings.user_name.c_str());
			syslog(LOG_DEBUG, "spice-vdagent check command: %s", vdagent_check_cmds);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'user-name'.");
        }
    } else if (name == "draw-cursor") {
        try {
            settings.draw_cursor = std::stoi(value);
            return true;
        } catch (const std::exception &e) {
            throw std::runtime_error("Invalid value '" + value + "' for option 'draw-cursor'.");
        }
    }    

    return false;
}

void GstreamerPlugin::StoreEncodingOptions(const std::string &encoder_options)
{
    std::stringstream encoder_options_ss(encoder_options);
    std::string option_token;

    while (std::getline(encoder_options_ss, option_token, ',')) {
        size_t has_sep = option_token.find('=');
        if (has_sep == std::string::npos) {
            throw std::runtime_error("Invalid parameter for GStreamer encoder '" + settings.encoder
                                     + "': " + "separator not found in '" + option_token + "'.");

        }
        std::string name = option_token.substr(0, has_sep);
        std::string value = option_token.substr(has_sep + 1);

        if (StorePluginOption(name, value)) {
            continue;
        }

        settings.enc_props[name] = value;
    }
}

void GstreamerPlugin::ParseOptions(const ConfigureOption *options, const std::string &codec_name,
                                   const std::string &encoder_cfg)
{
    if (codec_name == "h264") {
        settings.codec = SPICE_VIDEO_CODEC_TYPE_H264;
    } else if (codec_name == "vp9") {
        settings.codec = SPICE_VIDEO_CODEC_TYPE_VP9;
    } else if (codec_name == "vp8") {
        settings.codec = SPICE_VIDEO_CODEC_TYPE_VP8;
    } else if (codec_name == "mjpeg") {
        settings.codec = SPICE_VIDEO_CODEC_TYPE_MJPEG;
    } else if (codec_name == "h265") {
        settings.codec = SPICE_VIDEO_CODEC_TYPE_H265;
    } else {
        throw std::runtime_error("Invalid value '" + codec_name + "' for GStreamer codec.");
    }

    size_t config_sep_pos = encoder_cfg.find(':');

    if (config_sep_pos == std::string::npos) {
        config_sep_pos = encoder_cfg.length();
    }

    settings.encoder = encoder_cfg.substr(0, config_sep_pos);

    if (settings.encoder.empty()) {
         throw std::runtime_error("Invalid GStreamer parameter 'gst." + codec_name + "=" +
                                  encoder_cfg + "': encoder cannot be empty. " +
                                  "Use 'auto' to pick up GST default encoder.");
    }

    if (settings.encoder == "auto") {
        if (config_sep_pos != encoder_cfg.length()) {
            throw std::runtime_error("Invalid parameter 'gst." + codec_name + "=" + encoder_cfg +
                                     "': automatically-selected encoder cannot be configured.");
        }
        settings.encoder = "";
    }

    for (; options->name; ++options) {
        StorePluginOption(options->name, options->value);
    }

    if (config_sep_pos == encoder_cfg.length()) {
        return;
    }

    std::string encoder_options(encoder_cfg.substr(config_sep_pos + 1));

    StoreEncodingOptions(encoder_options);
}

}}} //namespace spice::streaming_agent::gstreamer_plugin

using namespace spice::streaming_agent::gstreamer_plugin;

SPICE_STREAMING_AGENT_PLUGIN(agent)
{
    const std::string gst_prefix = "gst.";
    auto options = agent->Options();
    bool registered = false;

    gst_init(nullptr, nullptr);

    for (; options->name; ++options) {
        const std::string name = options->name;
        const std::string value = options->value;

        if (name.rfind(gst_prefix, 0) == 0) {
            auto plugin = std::make_shared<GstreamerPlugin>();
            const std::string codec_name = name.substr(gst_prefix.length());

            plugin->ParseOptions(agent->Options(), codec_name, value);
            agent->Register(plugin);
            registered = true;
        }
    }

    if (!registered) {
        auto plugin = std::make_shared<GstreamerPlugin>();
        plugin->ParseOptions(agent->Options(), "vp8", "auto");
        agent->Register(plugin);
    }

    return true;
}
