/**
 *******************************************************************************
*           Copyright (C), 2023, CHANGSHA JINGJIAMICRO ELECTRONICS CO., LTD.
*******************************************************************************
* @file          jmfbc-plugin.cpp
* @brief         a plugin for spice-streaming-agent
* <AUTHOR>
* @version       1.0.0
* @date          2023/7/20\n
* @htmlonly
* <span style="font-weight: bold">更新历史</span> 
* @endhtmlonly
* 版本号|说明|修订者|修订日期
* ------|----|------|--------
* v1.0.0|创建文档|fengjian2548|2023/7/20
*******************************************************************************/


#include <config.h>
#include <spice-streaming-agent/x11-display-info.hpp>
#include <spice-streaming-agent/plugin.hpp>
#include <spice-streaming-agent/frame-capture.hpp>

#include <cstring>
#include <exception>
#include <stdexcept>
#include <sstream>
#include <memory>
#include <syslog.h>
#include <unistd.h>
#include <fcntl.h>
#include <list>
#include <X11/Xlib.h>
#include <va/va_drm.h>
#include <dlfcn.h>
#include <sys/time.h>
#include <chrono>

#include "frame-log.hpp"

/* SDK headers */
#include "jmfbc.h"
#include "jmenc.h"
// #include "Jmfbc_utils.h"

namespace spice {
namespace streaming_agent {
namespace jmsdk_plugin {

struct JmFBCOutData {
	unsigned long virtAddr;
	unsigned long phyAddr;
	unsigned int size;
	unsigned int width;
	unsigned int height;
	unsigned long long encode_order;
	uint64_t ts;
	uint64_t enc_ts;
};

struct JMFBCSettings
{
	SpiceVideoCodecType codec = SPICE_VIDEO_CODEC_TYPE_H264;
	int yuv444 = 0;
	int buildin_pp = 1;
	int fps = 60;
	int fpsi = 300;

	std::string drm_name = "/dev/dri/card1"; // not used
	std::string display = "0"; // not used

	int screen_id = 0; // screen index
	std::string output_name = ""; // the output name to be captured

	unsigned int capx = 0;  /* the sub region of drm framebuffer to be captured */
	unsigned int capy = 0;
	unsigned int capw = 0;
	unsigned int caph = 0;

	int wait_vblank = 1; // wait vblank before capture

	//
	// bellowing setting items are for jmfbc 
	//

	// rate control
	int rate_control = -1;
	int bitrate = -1;
	int peak_bitrate = -1;
	int quality_level = -1;
	int init_qp = -1;
	int min_qp = -1;
	int max_qp = -1;
	int qp_i = -1;
	int qp_p = -1;
	int qp_b = -1;

	// frame type control
	int keyframe_period = -1; // in H264/H265, it is GOP length
	int i_period = -1; // 
	int ip_period = -1;

	int async_enc = 0; // async encode mode
	std::string jmfbc_path = ""; // absolute path to the lib
	int watermark_enable = 0;  // watermark switch
	std::string watermark_text = "JINGJIA  MICRO  2023";
	int capture_mode = 0;  // 0:kms grab, 1:to sys 2: to GL 3: app capture
	int diffmap = 0;  // diffmap on/off
	std::string profile = "baseline";

	void dump()
	{
		syslog(LOG_DEBUG, "jmfbc-plugin settings");
		syslog(LOG_DEBUG, "  codec: 0x%x", codec);
		syslog(LOG_DEBUG, "  yuv444: 0x%x", yuv444);
		syslog(LOG_DEBUG, "  profile: %s", profile);
		syslog(LOG_DEBUG, "  buildin_pp: 0x%x", buildin_pp);
		syslog(LOG_DEBUG, "  jmfbc path: %s", jmfbc_path.c_str());
		syslog(LOG_DEBUG, "  watermark_enable: 0x%x", watermark_enable);
		syslog(LOG_DEBUG, "  capture_mode: 0x%x", capture_mode);
		syslog(LOG_DEBUG, "  diffmap: 0x%x", diffmap);
		syslog(LOG_DEBUG, "  fps: %d", fps);
		syslog(LOG_DEBUG, "  fpsi: 0x%x", fpsi);
		syslog(LOG_DEBUG, "  screen_id: 0x%x", screen_id);
		syslog(LOG_DEBUG, "  output_name: %s", output_name.c_str());
		syslog(LOG_DEBUG, "  region x,y,w,h: %d,%d,%d,%d", capx, capy, capw, caph);
		syslog(LOG_DEBUG, "  wait_vblank: 0x%x", wait_vblank);
		syslog(LOG_DEBUG, "  rate_control: 0x%x", rate_control);
		syslog(LOG_DEBUG, "  bitrate: %d", bitrate);
		syslog(LOG_DEBUG, "  peak_bitrate: %d", peak_bitrate);
		syslog(LOG_DEBUG, "  quality_level: %d", quality_level);
		syslog(LOG_DEBUG, "  init_qp: %d", init_qp);
		syslog(LOG_DEBUG, "  min_qp: %d", min_qp);
		syslog(LOG_DEBUG, "  max_qp: %d", max_qp);
		syslog(LOG_DEBUG, "  qp_i: %d", qp_i);
		syslog(LOG_DEBUG, "  qp_p: %d", qp_p);
		syslog(LOG_DEBUG, "  qp_b: %d", qp_b);
		syslog(LOG_DEBUG, "  keyframe_period: %d", keyframe_period);
		syslog(LOG_DEBUG, "  i_period: %d", i_period);
		syslog(LOG_DEBUG, "  ip_period: %d", ip_period);
		syslog(LOG_DEBUG, "  async_enc: %d", async_enc);
	}

	
};

#define JMFBCPlugin_Name "jmfbc"

class JMFBCPlugin final: public Plugin
{
public:
    FrameCapture *CreateCapture() override;
    unsigned Rank() override;
	std::string Name()  override;
    void ParseOptions(const ConfigureOption *options);
    JMFBCSettings Options() const;  // TODO unify on Settings vs Options
    SpiceVideoCodecType VideoCodecType() const override;
    static bool Register(Agent* agent);
private:
    JMFBCSettings settings;
};

class JMFBCFrameCapture final: public FrameCapture
{
public:
    JMFBCFrameCapture(const JMFBCSettings &settings);
    ~JMFBCFrameCapture();
    FrameInfo CaptureFrame() override;
    void Reset() override;
    SpiceVideoCodecType VideoCodecType() const override {
        return settings.codec;
    }
    std::vector<DeviceDisplayInfo> get_device_display_info() const override;

private:
	std::list<FrameInfo> frameList;	
    JMFBCSettings settings;
    Display *const dpy = NULL;

	bool isFirstFrame = true;

	pthread_mutex_t m_mutex = PTHREAD_MUTEX_INITIALIZER;
	pthread_cond_t m_cond = PTHREAD_COND_INITIALIZER;
	uint64_t frame_count = 0;
	FrameInfo m_finfo = { { 0, 0 }, NULL, 0 , FALSE };

	// for SDK
	static void* capture_thread(void *arg);
	static void frameCallback(JmFBCOutData *frame, void* data);
	JMENCSTATUS validateEncodeGUID(void *encoder, GUID encodeGuid);
	void *libFBC = NULL; 
	void *libEnc = NULL;
	JM_ENCODE_API_FUNCTION_LIST pEncFn;
	JMFBC_API_FUNCTION_LIST pCapFn;
	pthread_t capThread;
	bool capThreadRunning = true;
	JMFBC_SESSION_HANDLE fbcHandle;
	void* encoder;
	JMFBC_SIZE originFrameSize = { 0, 0 }; // this the framebuffer size before crop
	void *ppBuffer; // the captured result, a sys address
	int *sysHandle; // the captured result, a fd

	uint64_t cap_all_time = 0;
	bool stop = false;
};


static uint64_t get_utime()
{
    auto now = std::chrono::system_clock::now().time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(now).count();
}


JMFBCFrameCapture::JMFBCFrameCapture(const JMFBCSettings& settings):
    settings(settings),dpy(XOpenDisplay(nullptr))
{
    if (!dpy)
        throw std::runtime_error("Unable to initialize X11");
}

JMFBCFrameCapture::~JMFBCFrameCapture()
{
	stop = true;
	int ret = pthread_join(capThread, NULL);
	syslog(LOG_DEBUG, "thread join %d", ret);
    XCloseDisplay(dpy);
	syslog(LOG_DEBUG, "JMFBCFrameCapture released");
}

void JMFBCFrameCapture::Reset()
{
 
}

/**
 * Checks that the requested codec is supported by the HW encoder
 *
 * \param [in] *encoder
 *   The handle to the encoder instance.
 * \param [in] encodeGUID
 *   The GUID corresponding to the codec requested.
 *
 * \return
 *   JM_ENC_SUCCESS in case of success, error code otherwise.
 */
JMENCSTATUS JMFBCFrameCapture::validateEncodeGUID(void *encoder, GUID encodeGuid)
{
    unsigned int nGuids = 0, i, encodeGuidCount = 0, codecFound = 0;
    GUID *encodeGuidArray = NULL;
    JMENCSTATUS status = JM_ENC_SUCCESS;

    status = pEncFn.jmEncGetEncodeGUIDCount(encoder, &encodeGuidCount);
    if (status != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to query number of supported codecs, "
                "status = %d\n", status);
        goto fail;
    }

    encodeGuidArray = (GUID *)malloc(sizeof(GUID) * encodeGuidCount);
    if (!encodeGuidArray) {
		syslog(LOG_ERR, "Failed to allocate GUID array, status = %d\n", status);
        goto fail;
    }

    status = pEncFn.jmEncGetEncodeGUIDs(encoder, encodeGuidArray,
                                        encodeGuidCount, &nGuids);
    if (status != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to query supported codecs, status = %d\n", status);
        goto fail;
    }

    for (i = 0; i < nGuids; i++) {
        if (memcmp(&encodeGuid, &encodeGuidArray[i], sizeof(GUID)) == 0) {
            codecFound = 1;
            break;
        }
    }

fail:
    if (encodeGuidArray) {
        free((void*)encodeGuidArray);
    }

    return codecFound ? JM_ENC_SUCCESS : status;
}

/*
	a frame encoding finished
*/
void JMFBCFrameCapture::frameCallback(JmFBCOutData *frame, void* data)
{
	JMFBCFrameCapture *inst = static_cast<JMFBCFrameCapture *>(data);

	FrameInfo finfo;
	finfo.size.width = frame->width;
    finfo.size.height = frame->height;
    finfo.buffer = malloc(frame->size);
	finfo.stream_start = FALSE;
	finfo.cap_ts = frame->ts;
	finfo.enc_ts = frame->enc_ts;
	finfo.cap_time = inst->cap_all_time;
	memcpy((void*)finfo.buffer, (const void*)frame->virtAddr, frame->size);
    finfo.buffer_size = frame->size;
	
	if (inst->frame_count == 0) {
		// the first frame
		finfo.stream_start = TRUE;
	}

	// let it to be encode time
	finfo.enc_ts = get_utime() - finfo.enc_ts;

	/*if (inst->settings.fpsi == 0 || inst->frame_count % inst->settings.fpsi == 0) {
		syslog(LOG_DEBUG, "encode time %llu us", finfo.enc_ts);
	}*/

	// enqueue
	pthread_mutex_lock(&inst->m_mutex);	
	inst->frameList.push_back(finfo);
	(inst->frame_count)++;
	pthread_mutex_unlock(&inst->m_mutex);	
}

void* JMFBCFrameCapture::capture_thread(void* data)
{
	JMFBCFrameCapture* inst = static_cast<JMFBCFrameCapture*>(data);

	syslog(LOG_DEBUG, "jmfbc-plugin capture thread running");
	inst->capThreadRunning = true;
	
	inst->settings.dump();

	JM_ENC_PIC_PARAMS encParams;
	uint64_t n = 0;
	int bufferSize = 0;
	JM_ENC_REGISTERED_PTR registeredResources[64] = { NULL };
	JMFBC_DESTROY_CAPTURE_SESSION_PARAMS destroyCaptureParams;
	JMFBC_FRAME_GRAB_INFO grabInfo;
	struct timeval tval;
	uint64_t start_utime;
	uint32_t uof;
	GUID encodeGuid;
	JMFBC_BOX cap_box;

	PJMFBCCREATEINSTANCE JmFBCCreateInstance_ptr = NULL;
	PJMENCAPICREATEINSTANCE JmEncodeAPICreateInstance_ptr = NULL;

	JMFBCSTATUS fbcStatus;
	JMFBC_BOOL fbcBool;
	JMENCSTATUS encStatus;

	JMFBC_CREATE_HANDLE_PARAMS createHandleParams;
	JMFBC_DESTROY_HANDLE_PARAMS destroyHandleParams;
	JM_ENC_OUTPUT_PTR outputBuffer = NULL;

	// JmFBCUtilsPrintVersions(APP_VERSION);

	/*
	* Dynamically load the JmFBC library.
	* use LD_LIBRARY_PATH point to the dir of the lib
	*/
	inst->libFBC = dlopen(inst->settings.jmfbc_path.c_str(), RTLD_NOW);
	if (inst->libFBC == NULL) {
		syslog(LOG_ERR, "Unable to open %s, %s", inst->settings.jmfbc_path.c_str(), strerror(errno));
		goto enc_fail;
	}

	/*
	* Dynamically load the NvEncodeAPI library.
	*/
	inst->libEnc = inst->libFBC;

	/*
	* Resolve the 'JmFBCCreateInstance' symbol that will allow us to get
	* the API function pointers.
	*/
	JmFBCCreateInstance_ptr = (PJMFBCCREATEINSTANCE)dlsym(inst->libFBC, "jmFBCCreateInstance");
	if (JmFBCCreateInstance_ptr == NULL) {
		syslog(LOG_ERR, "Unable to resolve symbol 'JmFBCCreateInstance'");
		goto enc_fail;
	}

	/*
	* Create an JmFBC instance.
	*
	* API function pointers are accessible through pCapFn.
	*/
	memset(&(inst->pCapFn), 0, sizeof(inst->pCapFn));
	inst->pCapFn.version = JMFBC_VERSION;
	fbcStatus = JmFBCCreateInstance_ptr(&(inst->pCapFn));
	if (fbcStatus != JMFBC_SUCCESS) {
		syslog(LOG_ERR, "Unable to create JmFBC instance (status: %d)", fbcStatus);
		goto enc_fail;
	}

	/*
	* Resolve the 'JmEncodeAPICreateInstance_ptr' symbol that will allow us to get
	* the API function pointers.
	*/
	JmEncodeAPICreateInstance_ptr = (PJMENCAPICREATEINSTANCE)dlsym(inst->libEnc, "jmEncodeAPICreateInstance");
	if (JmEncodeAPICreateInstance_ptr == NULL) {
		syslog(LOG_ERR, "Unable to resolve symbol 'JmEncodeAPICreateInstance_ptr'");
		goto enc_fail;
	}

	/*
	* Create an NvEncodeAPI instance.
	*
	* API function pointers are accessible through pEncFn.
	*/
	memset(&(inst->pEncFn), 0, sizeof(inst->pEncFn));
	inst->pEncFn.version = JM_ENCODE_API_FUNCTION_LIST_VER;
	encStatus = JmEncodeAPICreateInstance_ptr(&(inst->pEncFn));
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Unable to create NvEncodeAPI instance (status: %d)", encStatus);
		goto enc_fail;
	}

	JM_ENC_CREATE_HANDLE_PARAMS chp;
	encStatus = inst->pEncFn.jmEncCreateHandle(&inst->encoder, &chp);
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to jmEncCreateHandle, status = %d\n", encStatus);
		goto enc_fail;
	}

	/*
	* Create a session handle that is used to identify the client.
	*/
	memset(&createHandleParams, 0, sizeof(createHandleParams));
	createHandleParams.version = JMFBC_CREATE_HANDLE_PARAMS_VER;
	fbcStatus = inst->pCapFn.jmFBCCreateHandle(&inst->fbcHandle, &createHandleParams);
	if (fbcStatus != JMFBC_SUCCESS) {
		syslog(LOG_ERR, "Unable to create fbc handle %d", fbcStatus);
		goto enc_fail;
	}

	/*
	* Retrieve the size of framebuffer.
	*/
	JMFBC_GET_STATUS_PARAMS statusParams;
	memset(&statusParams, 0, sizeof(statusParams));
	statusParams.version = JMFBC_GET_STATUS_PARAMS_VER;
	fbcStatus = inst->pCapFn.jmFBCGetStatus(inst->fbcHandle, &statusParams);
	if (fbcStatus != JMFBC_SUCCESS) {
		syslog(LOG_ERR, "Unable to get fbc status %d", fbcStatus);
		goto enc_fail;
	}

	syslog(LOG_DEBUG, "Screen size is %dx%d", statusParams.screenSize.w, statusParams.screenSize.h);

	/*
	* Create a capture session.
	*/
	JMFBC_CREATE_CAPTURE_SESSION_PARAMS createCaptureParams;
	memset(&createCaptureParams, 0, sizeof(createCaptureParams));
	createCaptureParams.version = JMFBC_CREATE_CAPTURE_SESSION_PARAMS_VER;
	// createCaptureParams.bWithCursor				= JMFBC_TRUE; // not usable
	// createCaptureParams.originFrameSize			= originFrameSize; // resize not usable now
	createCaptureParams.trackingType = JMFBC_TRACKING_OUTPUT;

	if (inst->settings.output_name != "") {
		for (int i = 0; i < statusParams.outputNum; i++) {
			syslog(LOG_DEBUG, "by name: %s", statusParams.outputs[i].name);
			if (strncasecmp(statusParams.outputs[i].name, inst->settings.output_name.c_str(), strlen(statusParams.outputs[i].name)) == 0) {
				createCaptureParams.outputId = statusParams.outputs[i].id;
				syslog(LOG_DEBUG, "by name: capture %s, crtc 0x%x", statusParams.outputs[i].name, statusParams.outputs[i].id);
				break;
			}
		}
	}
	if (createCaptureParams.outputId == 0) {
		createCaptureParams.outputId = statusParams.outputs[inst->settings.screen_id].id;
		syslog(LOG_DEBUG, "by index: capture %s, crtc 0x%x", statusParams.outputs[inst->settings.screen_id].name, statusParams.outputs[inst->settings.screen_id].id);
	}
	if (inst->settings.capw == 0 || inst->settings.caph == 0) {
		inst->settings.capx = 0;
		inst->settings.capy = 0;
		inst->settings.capw = statusParams.outputs[inst->settings.screen_id].trackedBox.w;
		inst->settings.caph = statusParams.outputs[inst->settings.screen_id].trackedBox.h;
	}

	syslog(LOG_DEBUG, "crop size %d,%d %dx%d", inst->settings.capx, inst->settings.capy, inst->settings.capw, inst->settings.caph);
	syslog(LOG_DEBUG, "capture_mode %d", inst->settings.capture_mode);

	/* this is the size captured */
	inst->originFrameSize.w = statusParams.outputs[inst->settings.screen_id].trackedBox.w;
	inst->originFrameSize.h = statusParams.outputs[inst->settings.screen_id].trackedBox.h;
	cap_box = { inst->settings.capx, inst->settings.capy, inst->settings.capw, inst->settings.caph };
	createCaptureParams.captureBox = cap_box;
	createCaptureParams.pushModel = JMFBC_FALSE; // NOT USED
	createCaptureParams.frameRate = inst->settings.fps;
	if (inst->settings.capture_mode == 0) {
		createCaptureParams.captureType = JMFBC_CAPTURE_TO_DRM;
	}
	else if (inst->settings.capture_mode == 1) {
		createCaptureParams.captureType = JMFBC_CAPTURE_TO_SYS;
	}
	fbcStatus = inst->pCapFn.jmFBCCreateCaptureSession(inst->fbcHandle, &createCaptureParams);
	if (fbcStatus != JMFBC_SUCCESS) {
		syslog(LOG_ERR, "create a capture session failed, %d", fbcStatus);
		goto enc_fail;
	}


	if (inst->settings.capture_mode == 0) {
		/*
		* Set up the capture.
		*/
		JMFBC_TODRM_SETUP_PARAMS setupParams;
		memset(&setupParams, 0, sizeof(setupParams));
		setupParams.version = JMFBC_TODRM_SETUP_PARAMS_VER;
		setupParams.bufferFormat = JMFBC_BUFFER_FORMAT_BGRA; /* desired format, for now, always specify 'JMFBC_BUFFER_FORMAT_BGRA' */
		setupParams.diffMapScalingFactor = 16;
		setupParams.withDiffMap = (JMFBC_BOOL)inst->settings.diffmap;
		fbcStatus = inst->pCapFn.jmFBCToDrmSetUp(inst->fbcHandle, &setupParams);
		if (fbcStatus != JMFBC_SUCCESS) {
			syslog(LOG_ERR, "setup capture failed, %d", fbcStatus);
			goto enc_fail;
		}
		inst->sysHandle = (int*)setupParams.handles[0];  // to hold the captured results...
	}
	else if (inst->settings.capture_mode == 1) {
		/*
		* Set up the capture.
		*/
		JMFBC_TOSYS_SETUP_PARAMS setupParams;
		memset(&setupParams, 0, sizeof(setupParams));
		setupParams.version = JMFBC_TOSYS_SETUP_PARAMS_VER;
		setupParams.bufferFormat = JMFBC_BUFFER_FORMAT_BGRA; /* desired format, for now, always specify 'JMFBC_BUFFER_FORMAT_BGRA' */
		setupParams.diffMapScalingFactor = 16;
		setupParams.withDiffMap = (JMFBC_BOOL)inst->settings.diffmap;
		fbcStatus = inst->pCapFn.jmFBCToSysSetUp(inst->fbcHandle, &setupParams);
		if (fbcStatus != JMFBC_SUCCESS) {
			syslog(LOG_ERR, "setup capture failed, %d", fbcStatus);
			goto enc_fail;
		}
		inst->sysHandle = (int*)setupParams.handles[0];  // to hold the captured results...
	}

	/*
	* Create an encoder session
	*/
	JM_ENC_OPEN_ENCODE_SESSION_PARAMS encodeSessionParams;
	memset(&encodeSessionParams, 0, sizeof(encodeSessionParams));
	encodeSessionParams.version = JM_ENC_OPEN_ENCODE_SESSION_PARAMS_VER;
	encodeSessionParams.apiVersion = JMENCAPI_VERSION;
	encodeSessionParams.deviceType = JM_ENC_DEVICE_TYPE_VAAPI;
	encStatus = inst->pEncFn.jmEncOpenEncodeSession(inst->encoder, &encodeSessionParams);
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to open an encoder session, status = %d", encStatus);
		goto enc_fail;
	}

	/*
	* Validate the codec requested
	*/
	encodeGuid = ((inst->settings.codec == SPICE_VIDEO_CODEC_TYPE_H264) ? JM_ENC_CODEC_H264_GUID : JM_ENC_CODEC_HEVC_GUID);
	encStatus = inst->validateEncodeGUID(inst->encoder, encodeGuid);
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "validateEncodeGUID failed", encStatus);
		goto enc_fail;
	}

	JM_ENC_PRESET_CONFIG presetConfig;
	memset(&presetConfig, 0, sizeof(presetConfig));
	presetConfig.version = JM_ENC_PRESET_CONFIG_VER;
	presetConfig.presetCfg.version = JM_ENC_CONFIG_VER;
	encStatus = inst->pEncFn.jmEncGetEncodePresetConfig(inst->encoder, encodeGuid, JM_ENC_PRESET_LOW_LATENCY_HP_GUID, &presetConfig);
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to obtain preset settings, status = %d", encStatus);
		goto enc_fail;
	}

	if (inst->settings.rate_control >= 0) {
		presetConfig.presetCfg.rcParams.rateControlMode = (JM_ENC_PARAMS_RC_MODE)inst->settings.rate_control;
	}
	if (inst->settings.peak_bitrate >= 0) {
		presetConfig.presetCfg.rcParams.maxBitRate = inst->settings.peak_bitrate;
	}
	if (inst->settings.bitrate >= 0) {
		presetConfig.presetCfg.rcParams.averageBitRate = inst->settings.bitrate;
	}
	if (inst->settings.init_qp >= 0){
		presetConfig.presetCfg.rcParams.initialRCQP.qpIntra = inst->settings.init_qp;
	}
	if (inst->settings.min_qp >= 0) {
		presetConfig.presetCfg.rcParams.minQP.qpIntra = inst->settings.min_qp;
	}
	if (inst->settings.max_qp >= 0) {
		presetConfig.presetCfg.rcParams.maxQP.qpIntra = inst->settings.max_qp;
	}
	if (inst->settings.qp_i >= 0) {
		presetConfig.presetCfg.rcParams.constQP.qpIntra = inst->settings.qp_i;
	}
	if (inst->settings.qp_p >= 0) {
		presetConfig.presetCfg.rcParams.constQP.qpInterP = inst->settings.qp_p;
	}
	if (inst->settings.qp_b >= 0) {
		presetConfig.presetCfg.rcParams.constQP.qpInterB = inst->settings.qp_b;
	}
	if (inst->settings.quality_level >= 0) {
		presetConfig.presetCfg.rcParams.targetQuality = inst->settings.quality_level; // refer to hardware spec
	}
	if (inst->settings.keyframe_period >= 0) {
		presetConfig.presetCfg.gopLength = inst->settings.keyframe_period;
	}
	if (inst->settings.i_period >= 0) {
		presetConfig.presetCfg.encodeCodecConfig.h264Config.idrPeriod = inst->settings.i_period;
	}
	if (inst->settings.ip_period >= 0) {
		presetConfig.presetCfg.frameIntervalP = inst->settings.ip_period; // set to 1, means there is no B frames at all
	}
	
	presetConfig.presetCfg.encodeCodecConfig.h264Config.chromaFormatIDC = 1; // 1: 420 3: 444

	if (inst->settings.codec == SPICE_VIDEO_CODEC_TYPE_H264) {
		if (strcmp(inst->settings.profile.c_str(), "auto") == 0) {
			presetConfig.presetCfg.profileGUID = JM_ENC_CODEC_PROFILE_AUTOSELECT_GUID;
		}
		else if (strcmp(inst->settings.profile.c_str(), "baseline") == 0) {
			presetConfig.presetCfg.profileGUID = JM_ENC_H264_PROFILE_BASELINE_GUID;

		}
		else if (strcmp(inst->settings.profile.c_str(), "main") == 0) {
			presetConfig.presetCfg.profileGUID = JM_ENC_H264_PROFILE_MAIN_GUID;
		}
		else if (strcmp(inst->settings.profile.c_str(), "high") == 0) {
			presetConfig.presetCfg.profileGUID = JM_ENC_H264_PROFILE_HIGH_GUID;
		}
	}
	else {
		if (strcmp(inst->settings.profile.c_str(), "main") == 0) {
			presetConfig.presetCfg.profileGUID = JM_ENC_HEVC_PROFILE_MAIN_GUID;
		}
		else if (strcmp(inst->settings.profile.c_str(), "main444") == 0) {
			presetConfig.presetCfg.profileGUID = JM_ENC_HEVC_PROFILE_MAIN_444_GUID;
			presetConfig.presetCfg.encodeCodecConfig.h264Config.chromaFormatIDC = 3; // 1: 420 3: 444
		}
	}
		
	/*
	* Initialize the encode session
	*/
	JM_ENC_INITIALIZE_PARAMS initParams; 
	memset(&initParams, 0, sizeof(initParams));
	initParams.version = JM_ENC_INITIALIZE_PARAMS_VER;
	initParams.encodeGUID = encodeGuid;
	initParams.pEncodeConfig = &presetConfig.presetCfg;
	initParams.encodeWidth = createCaptureParams.captureBox.w;
	initParams.encodeHeight = createCaptureParams.captureBox.h;
	initParams.frameRateNum = inst->settings.fps;
	initParams.frameRateDen = 1;
	initParams.enableEncodeAsync = 0; //
	
	encStatus = inst->pEncFn.jmEncInitializeEncoder(inst->encoder, &initParams);
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to initialize the encode session, status = %d", encStatus);
		goto enc_fail;
	}

	/*
	* Create a bitstream buffer to hold the output
	*/
	JM_ENC_CREATE_BITSTREAM_BUFFER bitstreamBufferParams;
	memset(&bitstreamBufferParams, 0, sizeof(bitstreamBufferParams));
	bitstreamBufferParams.version = JM_ENC_CREATE_BITSTREAM_BUFFER_VER;
	bitstreamBufferParams.size = 10 * 1024 * 1024;
	encStatus = inst->pEncFn.jmEncCreateBitstreamBuffer(inst->encoder, &bitstreamBufferParams);
	if (encStatus != JM_ENC_SUCCESS) {
		syslog(LOG_ERR, "Failed to create a bitstream buffer, status = %d", encStatus);
		goto enc_fail;
	}
	outputBuffer = (JM_ENC_OUTPUT_PTR)bitstreamBufferParams.bitstreamBuffer; // this is the index of encoded va buffer
			
	/*
	* Start capturing and encoding frames.
	*/ 

	gettimeofday(&tval, NULL);
	start_utime =  tval.tv_sec * 1000000 + tval.tv_usec;
	uof = 1000000 / inst->settings.fps;

	syslog(LOG_DEBUG, "start capturing");
	
	while (!inst->stop) {

		 // enqueue
		pthread_mutex_lock(&inst->m_mutex);
		if (inst->frameList.size() > 1) {
			pthread_mutex_unlock(&inst->m_mutex);
			syslog(LOG_WARNING, "cache list size > 1, %d, sending out too much slow ?", inst->frameList.size());
			usleep(100 * 1000);
			continue;
		}
		pthread_mutex_unlock(&inst->m_mutex);

		// frame rate control
		gettimeofday(&tval, NULL);
		uint64_t now_utime =  tval.tv_sec * 1000000 + tval.tv_usec;
		int64_t wantedframes = ((now_utime - start_utime) / 1000000) * inst->settings.fps + ((now_utime - start_utime) % 1000000) / uof ;
		int32_t diff = wantedframes - n;
		if (diff < 0) {
			usleep(-diff * uof);
		}

		uint64_t time_cap = get_utime();

		if (inst->settings.capture_mode == 0) {
			JMFBC_TODRM_GRAB_FRAME_PARAMS grabParams;
			memset(&grabParams, 0, sizeof(grabParams));
			grabParams.version = JMFBC_TODRM_GRAB_FRAME_PARAMS_VER;
			grabParams.pFrameGrabInfo = &grabInfo;
			grabParams.timeoutMs = 100;
			if (inst->settings.wait_vblank) {
				grabParams.flags = JMFBC_TODRM_GRAB_FLAGS_NOFLAGS;
			}
			else {
				grabParams.flags = JMFBC_TODRM_GRAB_FLAGS_NOWAIT;
			}
			//syslog(LOG_DEBUG, "kms capturing");
			
			/*
			* Capture a frame.
			*/
			fbcStatus = inst->pCapFn.jmFBCToDrmGrabFrame(inst->fbcHandle, &grabParams);
			if (fbcStatus == JMFBC_ERR_TIMEOUT || fbcStatus == JMFBC_ERR_INTERRUPT) {
				continue;
			}
			else if (fbcStatus != JMFBC_SUCCESS) {
				syslog(LOG_ERR, "jmFBCToDrmGrabFrame fail");
				goto enc_fail;
			}
		}
		else if (inst->settings.capture_mode == 1) {
			JMFBC_TOSYS_GRAB_FRAME_PARAMS grabParams;
			memset(&grabParams, 0, sizeof(grabParams));
			grabParams.version = JMFBC_TOSYS_GRAB_FRAME_PARAMS_VER;
			grabParams.pFrameGrabInfo = &grabInfo;
			grabParams.timeoutMs = 100;
			if (inst->settings.wait_vblank) {
				grabParams.flags = JMFBC_TOSYS_GRAB_FLAGS_NOFLAGS;
			}
			else {
				grabParams.flags = JMFBC_TOSYS_GRAB_FLAGS_NOWAIT;
			}
			//syslog(LOG_DEBUG, "sys capturing");

			/*
			* Capture a frame.
			*/
			fbcStatus = inst->pCapFn.jmFBCToSysGrabFrame(inst->fbcHandle, &grabParams);
			if (fbcStatus == JMFBC_ERR_TIMEOUT || fbcStatus == JMFBC_ERR_INTERRUPT) {
				continue;
			}
			else if (fbcStatus != JMFBC_SUCCESS) {
				syslog(LOG_ERR, "jmFBCToSysGrabFrame fail");
				goto enc_fail;
			}
		}
	
		uint64_t time_enc = get_utime();
		inst->cap_all_time = time_enc - time_cap;

		/*
		* Map the frame for use by the encoder.
		*/
		JM_ENC_REGISTER_RESOURCE registerParams;
		memset(&registerParams, 0, sizeof(registerParams));
		registerParams.version = JM_ENC_REGISTER_RESOURCE_VER;
		registerParams.resourceType = JM_ENC_INPUT_RESOURCE_TYPE_DRM;
		
		registerParams.width = grabInfo.width;
		registerParams.height = grabInfo.height;
		registerParams.pitch = grabInfo.pitch;
		registerParams.bpp = grabInfo.bpp;

		registerParams.cropX = grabInfo.xoff;
		registerParams.cropY = grabInfo.yoff;
		registerParams.cropWidth = grabInfo.width;
		registerParams.cropHeight = grabInfo.height;

		/*if (inst->settings.capture_mode == 0) {
			registerParams.cropX = inst->settings.capx;
			registerParams.cropY = inst->settings.capy;
			registerParams.cropWidth = inst->settings.capw;
			registerParams.cropHeight = inst->settings.caph;
		}
		else if (inst->settings.capture_mode == 1) {
			// as to sys capture has cropped the images, encoder no longer need to do this
			registerParams.cropX = 0;
			registerParams.cropY = 0;
			registerParams.cropWidth = grabInfo.width;
			registerParams.cropHeight = grabInfo.height;
		}*/


		registerParams.pResourceToRegister = (void*)(*(inst->sysHandle));
		// printf("fdfdfdfd pResourceToRegister  %d\n", *(inst->sysHandle));
		registerParams.bufferFormat = JM_ENC_BUFFER_FORMAT_ABGR;

		encStatus = inst->pEncFn.jmEncRegisterResource(inst->encoder, &registerParams);
		if (encStatus != JM_ENC_SUCCESS) {
			syslog(LOG_ERR, "Failed to register the resource, status = %d", encStatus);
			goto enc_fail;
		}

		if (inst->settings.watermark_enable) {

			JM_ENC_WATERMARK_PARAMS watermarkParams;
			// strncpy(watermarkParams.text, "JINGJIA  MICRO  2023", strlen("JINGJIA  MICRO  2023"));
			strncpy(watermarkParams.text, inst->settings.watermark_text.c_str(), strlen(inst->settings.watermark_text.c_str()));
			watermarkParams.fontSize = 1200;
			watermarkParams.letterSpacing = 10; // 10%
			watermarkParams.textColor[0] = 204;
			watermarkParams.textColor[1] = 204;
			watermarkParams.textColor[2] = 230;
			watermarkParams.transparency = 80;
			watermarkParams.angle = 0;
			watermarkParams.width = registerParams.width;
			watermarkParams.height = registerParams.height;
			watermarkParams.outBuffer = 0; 				  //output
			watermarkParams.buffer = registerParams.registeredResource; 	  //input
			inst->pEncFn.jmEncSetWatermark(inst->encoder, &watermarkParams);
			// printf("watermarkParams.framebuffer_fd =%d\n", watermarkParams.framebuffer_fd);
			// *(inst->ppBuffer) = (void*)watermarkParams.framebuffer_fd;

			inst->pEncFn.jmEncUnregisterResource(inst->encoder, registerParams.registeredResource);		  //取消注册

			/*
			* Map the frame for use by the encoder.
			*/
			// JM_ENC_REGISTER_RESOURCE registerParams;
			memset(&registerParams, 0, sizeof(registerParams));
			registerParams.version = JM_ENC_REGISTER_RESOURCE_VER;
			registerParams.resourceType = JM_ENC_INPUT_RESOURCE_TYPE_DRM;
			registerParams.width = grabInfo.width;
			registerParams.height = grabInfo.height;
			registerParams.pitch = grabInfo.pitch;
			registerParams.bpp = grabInfo.bpp;
			registerParams.cropX = inst->settings.capx;
			registerParams.cropY = inst->settings.capy;
			registerParams.cropWidth = inst->settings.capw;
			registerParams.cropHeight = inst->settings.caph;
			registerParams.pResourceToRegister = (void*)watermarkParams.outBuffer;
			registerParams.bufferFormat = JM_ENC_BUFFER_FORMAT_ABGR;

			encStatus = inst->pEncFn.jmEncRegisterResource(inst->encoder, &registerParams);
			if (encStatus != JM_ENC_SUCCESS) {
				syslog(LOG_ERR, "Failed to register the resource, status = %d", encStatus);
				goto enc_fail;
			}

	   }

	   encParams.inputBuffer = registerParams.registeredResource;
	   encParams.bufferFmt = registerParams.bufferFormat;
	   encParams.outputBitstream = outputBuffer;
	   //encParams.frameIdx = encParams.inputTimeStamp = n;
		  
		/*
		* Encode the frame.
		*/
		encStatus = inst->pEncFn.jmEncEncodePicture(inst->encoder, &encParams);
		if (encStatus != JM_ENC_SUCCESS) {
			syslog(LOG_ERR, "Failed to encode frame, status = %d", encStatus);
			goto enc_fail;
		} 
		else {
			/*
			* Get the bitstream and dump to file.
			*/
			JM_ENC_LOCK_BITSTREAM lockParams;	
			memset(&lockParams, 0, sizeof(lockParams));
			lockParams.version = JM_ENC_LOCK_BITSTREAM_VER;
			lockParams.pOutputBitstream = outputBuffer; // in async mode, this is a vasurface id
			encStatus = inst->pEncFn.jmEncLockBitstream(inst->encoder, &lockParams);
			if (encStatus == JM_ENC_SUCCESS) {
				bufferSize = lockParams.bitstreamSizeInBytes;
				if (bufferSize == 0) {
					/*
					* We failed to obtain the bitstream for some reason; it's better
					* to terminate.
					*/
					syslog(LOG_ERR, "Failed to get output size");
					goto enc_fail;
				}
				JmFBCOutData outFrame;
				outFrame.virtAddr = (long unsigned int)lockParams.pBitstreamBufferPtr;
				outFrame.size = bufferSize;
				outFrame.width = inst->settings.capw;
				outFrame.height = inst->settings.caph;
				outFrame.ts = time_cap;
				outFrame.enc_ts = time_enc;
				frameCallback(&outFrame, inst);
				encStatus = inst->pEncFn.jmEncUnlockBitstream(inst->encoder, outputBuffer);
				if (encStatus != JM_ENC_SUCCESS) {
					/*
					* We usually shouln't be here.
					*/
					syslog(LOG_ERR, "Failed to unlock bitstream buffer, status = %d", encStatus);
					goto enc_fail;
				}
			} else {
				syslog(LOG_ERR, "Failed to lock bitstream buffer, status = %d", encStatus);
				goto enc_fail;
			}
		}

		encStatus = inst->pEncFn.jmEncUnregisterResource(inst->encoder, registerParams.registeredResource);
		if (encStatus != JM_ENC_SUCCESS) {
			syslog(LOG_ERR, "Failed to unregister the resource, status = %d\n", encStatus);
			goto enc_fail;
		}

		n++;
	}

	if (outputBuffer) {
		inst->pEncFn.jmEncDestroyBitstreamBuffer(inst->encoder, outputBuffer);
	}
	
	syslog(LOG_DEBUG, "captured %llu frames", n);
	syslog(LOG_DEBUG, "jmfbc-plugin destroying resources");

enc_fail:
	
	/*
	* Destroy the encode session
	*/
	encStatus = inst->pEncFn.jmEncDestroyEncoder(inst->encoder);
	if (encStatus != JM_ENC_SUCCESS) {
	   syslog(LOG_ERR, "jmEncDestroyEncoder fail, %d\n", encStatus);
	}
	   
	/*
	* Destroy capture session.
	*/
	memset(&destroyCaptureParams, 0, sizeof(destroyCaptureParams));
	destroyCaptureParams.version = JMFBC_DESTROY_CAPTURE_SESSION_PARAMS_VER;
	fbcStatus = inst->pCapFn.jmFBCDestroyCaptureSession(inst->fbcHandle, &destroyCaptureParams);
	if (fbcStatus != JMFBC_SUCCESS) {
		syslog(LOG_ERR, "jmFBCDestroyCaptureSession fail %d", fbcStatus);
	}

	/*
	* Destroy session handle, tear down more resources.
	*/
	memset(&destroyHandleParams, 0, sizeof(destroyHandleParams));
	destroyHandleParams.version = JMFBC_DESTROY_HANDLE_PARAMS_VER;
	fbcStatus = inst->pCapFn.jmFBCDestroyHandle(inst->fbcHandle, &destroyHandleParams);
	if (fbcStatus != JMFBC_SUCCESS) {
		syslog(LOG_ERR, "jmFBCDestroyHandle fail %d", fbcStatus);
	}

	syslog(LOG_DEBUG, "jmfbc-plugin capture thread exit");
	inst->capThreadRunning = false;
	
	return NULL;
}

FrameInfo JMFBCFrameCapture::CaptureFrame()
{
	if (!capThreadRunning) {
		syslog(LOG_ERR, "capture thread has exited, abort");
		exit(0);
	}
	
	if (isFirstFrame) {
		syslog(LOG_DEBUG, "start capture thread");
		pthread_create(&capThread, nullptr, capture_thread, this);
		isFirstFrame = false;
	}

	if (m_finfo.buffer) {
		free((void*)m_finfo.buffer);
	}
	
	m_finfo.buffer = NULL;
	m_finfo.buffer_size = 0;
	int wait_cnt = 0;
	
	while (1) {
		pthread_mutex_lock(&m_mutex);
		if (frameList.size() > 0) {
		  	m_finfo = frameList.front();
		  	frameList.pop_front();
			pthread_mutex_unlock(&m_mutex);
		  	break; 
		}
		else {
			pthread_mutex_unlock(&m_mutex);
			usleep(1 * 1000);
			if ((++wait_cnt) > 500) {
				// syslog(LOG_WARNING, "there is no frame for 500 ms");
				break;
			}
		}
	}

    return m_finfo;
}

std::vector<DeviceDisplayInfo> JMFBCFrameCapture::get_device_display_info() const
{
    try {
        return get_device_display_info_drm(dpy);
    } catch (const std::exception &e) {
        syslog(LOG_WARNING, "Failed to get device info using DRM: %s. Using no-DRM fallback.",
               e.what());
        return get_device_display_info_no_drm(dpy);
    }
}

FrameCapture *JMFBCPlugin::CreateCapture()
{
    return new JMFBCFrameCapture(settings);
}

unsigned JMFBCPlugin::Rank()
{
    return SoftwareMin;
}

std::string JMFBCPlugin::Name() 
{
	return JMFBCPlugin_Name;
}

void JMFBCPlugin::ParseOptions(const ConfigureOption *options)
{
    for (; options->name; ++options) {
        const std::string name = options->name;
        const std::string value = options->value;
		syslog(LOG_DEBUG, "ParseOptions %s %s\n", name.c_str(), value.c_str());
        if (name == "framerate") {
            try {
                settings.fps = std::stoi(value);
            } catch (const std::exception &e) {
                throw std::runtime_error("Invalid value '" + value + "' for option 'framerate'.");
            }
        } else if (name == "profile") {
            try {
                settings.profile = value;
            } catch (const std::exception &e) {
                throw std::runtime_error("Invalid value '" + value + "' for option 'profile'.");
            }
        } else if (name == "peak-bitrate") {
	        try {
	            settings.peak_bitrate = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'peak-bitrate'.");
	        }
	    }  else if (name == "bitrate") {
	        try {
	            settings.bitrate = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'bitrate'.");
	        }
	    } else if (name == "rate-control") {
	        try {
	            settings.rate_control = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'rate-control'.");
	        }
	    } else if (name == "quality-level") {
	        try {
	            settings.quality_level = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'quality-level'.");
	        }
	    } else if (name == "init-qp") {
	        try {
	            settings.init_qp = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'init-qp'.");
	        }
	    } else if (name == "min-qp") {
	        try {
	            settings.min_qp = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'min-qp'.");
	        }
	    } else if (name == "max-qp") {
	        try {
	            settings.max_qp = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'max-qp'.");
	        }
	    } else if (name == "i-qp") {
	        try {
	            settings.qp_i = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'i-qp'.");
	        }
	    } else if (name == "p-qp") {
	        try {
	            settings.qp_p = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'p-qp'.");
	        }
	    } else if (name == "b-qp") {
	        try {
	            settings.qp_b = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'b-qp'.");
	        }
	    } else if (name == "keyframe-period") {
	        try {
	            settings.keyframe_period = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'keyframe-period'.");
	        }
	    } else if (name == "i-period") {
	        try {
	            settings.i_period = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'i-period'.");
	        }
	    } else if (name == "ip-period") {
	        try {
	            settings.ip_period = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'ip-period'.");
	        }
	    }
		else if (name == "codec") {
            try {
				int tmp_codec = (SpiceVideoCodecType)std::stoi(value);
				if (tmp_codec == 0) {
					settings.codec = SPICE_VIDEO_CODEC_TYPE_H264;
				}
				else {
					settings.codec = SPICE_VIDEO_CODEC_TYPE_H265;
				}
            } catch (const std::exception &e) {
                throw std::runtime_error("Invalid value '" + value + "' for option 'codec'.");
            }
        }
		else if (name == "yuv444") {
	        try {
	            settings.yuv444 = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'yuv444'.");
	        }
	    }
		else if (name == "buildin-pp") {
	        try {
	            settings.buildin_pp = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'buildin-pp'.");
	        }
	    }
		else if (name == "device") {
	        try {
	            settings.drm_name = value;
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'device'.");
	        }
        }
		else if (name == "capture-display") {
			try {
				settings.display = value;
			} catch (const std::exception &e) {
				throw std::runtime_error("Invalid value '" + value + "' for option 'capture-display'.");
			}
		}
		else if (name == "capx") {
	        try {
	            settings.capx = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'capx'.");
	        }
	    }
	    else if (name == "capy") {
	        try {
	            settings.capy = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'capy'.");
	        }
	    }
		else if (name == "capw") {
	        try {
	            settings.capw = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'capw'.");
	        }
	    }
		else if (name == "screen-id") {
	        try {
	            settings.screen_id = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'screen-id'.");
	        }
    	}
		else if (name == "async-enc") {
	        try {
	            settings.async_enc = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'async-enc'.");
	        }
    	}  
		else if (name == "caph") {
	        try {
	            settings.caph = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'caph'.");
	        }
	    }  
		else if (name == "jmfbc-path") {
	        try {
	            settings.jmfbc_path = value;
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'jmfbc_path'.");
	        }
	    }
		else if (name == "output-name") {
	        try {
	            settings.output_name = value;
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'output_name'.");
	        }
	    }
		else if (name == "fpsi") {
	        try {
	            settings.fpsi = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'fpsi'.");
	        }
	    }
		else if (name == "watermark") {
	        try {
	            settings.watermark_enable = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'watermark'.");
	        }
	    }
		else if (name == "usevblank") {
	        try {
	            settings.wait_vblank = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'usevblank'.");
	        }
	    }
		else if (name == "capture-mode") {
	        try {
	            settings.capture_mode = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'capture-mode'.");
	        }
	    }
		else if (name == "diffmap") {
	        try {
	            settings.diffmap = std::stoi(value);
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'diffmap'.");
	        }
	    }
		else if (name == "watermark-text") {
	        try {
	            settings.watermark_text = value;
	        } catch (const std::exception &e) {
	            throw std::runtime_error("Invalid value '" + value + "' for option 'watermark-text'.");
	        }
	    }

    }
}

JMFBCSettings JMFBCPlugin::Options() const
{
    return settings;
}

SpiceVideoCodecType JMFBCPlugin::VideoCodecType() const {
    return settings.codec;
}


}}} 

using namespace spice::streaming_agent::jmsdk_plugin;

SPICE_STREAMING_AGENT_PLUGIN(agent)
{
    auto options = agent->Options();
 
    auto plugin = std::make_shared<JMFBCPlugin>();
	
	try {
		plugin->ParseOptions(agent->Options());
	} catch (const std::exception &e) {
		syslog(LOG_ERR, "Error parsing plugin option: %s", e.what());
	}
	
	agent->Register(plugin);

    return true;
}
