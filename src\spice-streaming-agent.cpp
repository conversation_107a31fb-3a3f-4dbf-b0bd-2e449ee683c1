/* An implementation of a SPICE streaming agent
 *
 * \copyright
 * Copyright 2016-2017 Red Hat Inc. All rights reserved.
 */

#include "concrete-agent.hpp"
#include "mjpeg-fallback.hpp"
#include "cursor-updater.hpp"
#include "frame-log.hpp"
#include "stream-port.hpp"
#include "utils.hpp"
#include <spice-streaming-agent/error.hpp>

#include <spice/stream-device.h>
#include <spice/enums.h>

#include <spice-streaming-agent/frame-capture.hpp>
#include <spice-streaming-agent/plugin.hpp>

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <inttypes.h>
#include <string.h>
#include <getopt.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/time.h>
#include <poll.h>
#include <syslog.h>
#include <signal.h>
#include <algorithm>
#include <exception>
#include <stdexcept>
#include <memory>
#include <vector>
#include <string>
#include <X11/Xlib.h>
#include <X11/Xutil.h>
#include <pthread.h>
#include <X11/extensions/Xdamage.h>
#include <sys/stat.h>
#include <common/utils.h>

using namespace spice::streaming_agent;

class FormatMessage : public OutboundMessage<StreamMsgFormat, FormatMessage, STREAM_TYPE_FORMAT>
{
public:
    FormatMessage(unsigned w, unsigned h, uint8_t c) {}

    static size_t size()
    {
        return sizeof(PayloadType);
    }

    void write_message_body(StreamPort &stream_port, unsigned w, unsigned h, uint8_t c)
    {
        StreamMsgFormat msg{};
        msg.width = w;
        msg.height = h;
        msg.codec = c;

        stream_port.write(&msg, sizeof(msg));
    }
};

class FrameMessage : public OutboundMessage<StreamMsgData, FrameMessage, STREAM_TYPE_DATA>
{
public:
    FrameMessage(const void *frame, size_t length) : OutboundMessage(length) {}

    static size_t size(size_t length)
    {
        return sizeof(PayloadType) + length;
    }

    void write_message_body(StreamPort &stream_port, const void *frame, size_t length)
    {
        stream_port.write(frame, length);
    }
};

class CapabilitiesOutMessage : public OutboundMessage<StreamMsgCapabilities, CapabilitiesOutMessage, STREAM_TYPE_CAPABILITIES>
{
public:
    CapabilitiesOutMessage(const std::vector<bool> &capabilities) : OutboundMessage() {}

    static size_t size()
    {
        uint8_t caps[AgentCapabilitiesBytes];

        return sizeof(PayloadType) + sizeof(caps);
    }

    void write_message_body(StreamPort &stream_port, const std::vector<bool> &capabilities)
    {
        uint8_t caps[AgentCapabilitiesBytes] = {};
        size_t i = 0;

        for (auto cap: capabilities) {
            if (cap) {
                set_bitmap(i, caps);
            }
            i++;
        }
        stream_port.write(caps, sizeof(caps));
    }
};

class DeviceDisplayInfoMessage : public OutboundMessage<StreamMsgDeviceDisplayInfo, DeviceDisplayInfoMessage, STREAM_TYPE_DEVICE_DISPLAY_INFO>
{
public:
    DeviceDisplayInfoMessage(const DeviceDisplayInfo &info) : OutboundMessage(info) {}

    static size_t size(const DeviceDisplayInfo &info)
    {
        return sizeof(PayloadType) +
               std::min(info.device_address.length(), static_cast<size_t>(max_device_address_len)) +
               1;
    }

    void write_message_body(StreamPort &stream_port, const DeviceDisplayInfo &info)
    {
        std::string device_address = info.device_address;
        if (device_address.length() > max_device_address_len) {
            syslog(LOG_WARNING,
                   "device address of stream id %u is longer than %u bytes, trimming.",
                   info.stream_id, max_device_address_len);
            device_address = device_address.substr(0, max_device_address_len);
        }
        StreamMsgDeviceDisplayInfo strm_msg_info{};
        strm_msg_info.stream_id = info.stream_id;
        strm_msg_info.device_display_id = info.device_display_id;
        strm_msg_info.device_address_len = device_address.length() + 1;
        stream_port.write(&strm_msg_info, sizeof(strm_msg_info));
        stream_port.write(device_address.c_str(), device_address.length() + 1);
    }

private:
    static constexpr uint32_t max_device_address_len = 255;
};

static bool change_resolution_requested = false;
static bool streaming_requested = false;
static volatile  bool quit_requested = false;
static std::set<SpiceVideoCodecType> client_codecs;
static std::string plugin_name;
static std::string force_streaming = ""; // if no client connecting
static int runs_in_vm = 1;

static bool have_something_to_read(StreamPort &stream_port, bool blocking)
{
    struct pollfd pollfd = {stream_port.fd, POLLIN, 0};

    if (poll(&pollfd, 1, blocking ? -1 : 0) < 0) {
        if (errno == EINTR) {
            // report nothing to read, next iteration of the enclosing loop will retry
            return false;
        }

        throw IOError("poll failed on the device", errno);
    }

    if (pollfd.revents & POLLIN) {
        return true;
    }

    return false;
}

static void server_capabilities_received(StreamPort &stream_port,
                                         std::vector<bool> &server_capabilities)
{
#if 0 /* No capability defined at the moment */
    /* Check here if the server supports the capability */
    if (server_capabilities[STREAM_CAP_...]) {
        ...
    }
#endif
}

static void read_command_from_device(StreamPort &stream_port)
{
    InboundMessage in_message = stream_port.receive();

    switch (in_message.header.type) {
    case STREAM_TYPE_CAPABILITIES: {
        InCapabilitiesMessage msg = in_message.get_payload<InCapabilitiesMessage>();
        std::vector<bool> agent_capabilities(STREAM_CAP_END, false);

        server_capabilities_received(stream_port, msg.capabilities);

		syslog(LOG_INFO, "GOT CAPABILITIES message");
        // populate here the `agent_capabilities` vector
        // agent_capabilities[STREAM_CAP_...] = true;
        stream_port.send<CapabilitiesOutMessage>(agent_capabilities);
        return;
    }
    case STREAM_TYPE_NOTIFY_ERROR: {
        NotifyErrorMessage msg = in_message.get_payload<NotifyErrorMessage>();

        syslog(LOG_ERR, "Received NotifyError message from the server: %d - %s",
               msg.error_code, msg.message);
        return;
    }
    case STREAM_TYPE_START_STOP: {
        StartStopMessage msg = in_message.get_payload<StartStopMessage>();
        streaming_requested = msg.start_streaming;
        client_codecs = msg.client_codecs;

        syslog(LOG_INFO, "GOT START_STOP message -- request to %s streaming",
               streaming_requested ? "START" : "STOP");

		syslog(LOG_INFO, "GOT CAPABILITIES message, client_codecs:");
		for (auto i = client_codecs.begin(); i != client_codecs.end(); i++) {
		    syslog(LOG_INFO, "codec: %d", (*i));
		}
        return;
    }}

    throw std::runtime_error("UNKNOWN msg of type " + std::to_string(in_message.header.type));
}

static void read_command(StreamPort &stream_port, bool blocking)
{
    while (!quit_requested) {
        if (have_something_to_read(stream_port, blocking)) {
            read_command_from_device(stream_port);
            break;
        }

        if (!blocking) {
            break;
        }

        sleep(1);
    }
}

static void signal_handler(int intr)
{
	/*
		as signal handlers's context is never known, do things as simply as possible.  
	*/
    syslog(LOG_INFO, "got signal %d", intr);
 
    quit_requested = true;

    syslog(LOG_INFO, "exit signal handler");
}

static void register_interrupts(void)
{
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
}

static void usage(const char *progname)
{
    printf("usage: %s <options>\n", progname);
    printf("options are:\n");
    printf("\t-p portname  -- virtio-serial port to use\n");
    printf("\t-l file -- log frames to file\n");
    printf("\t--log-binary -- log binary frames (following -l)\n");
    printf("\t--log-categories -- log categories, separated by ':' (currently: frames)\n");
    printf("\t--plugins-dir=path -- change plugins directory\n");
    printf("\t-d -- enable debug logs\n");
    printf("\t-c variable=value -- change settings\n");
    printf("\t\tframerate = 1-100 (check 10,20,30,40,50,60)\n");
    printf("\t\tcapture = kms/x11\n");
    printf("\t\tgst.h264 = vaapih264enc/other gst plugin\n");
    printf("\t\tprofile = encoding profile\n");
    printf("\t\tdevice = /dev/dri/cardx\n");
    printf("\t\tcapture-display = :0/other x display\n");
    printf("\t\tusedamage = 0/1\n");
    printf("\t\tusevblank = 0/1\n");
    printf("\t\tcapx = spicify a integer value\n");
    printf("\t\tcapy = spicify a integer value\n");
    printf("\t\tcapw = spicify a integer value\n");
    printf("\t\tcaph = spicify a integer value\n");
    printf("\n");
    printf("\t-h or --help     -- print this help message\n");

    exit(1);
}

static std::string xdisp_name = ":0"; //NOT use it. we use DISPLAY env
static FILE* dbg_fd = NULL;
static Display *xdisp = NULL;
static gint damage_event_base;
static Damage damage;
static XserverRegion damage_region;
static GThread *xdamage_thread_id  = NULL;
static guint xwindow_w = -1u;
static guint xwindow_h = -1u;
static GThread *resolution_thread_id = NULL;
static GThread *bomb_thread_id = NULL;
static guint xwindow;
static guint fpsi = 300;
static int codec_type = 0; // 0:264 1:265
static int screen_id = 0;
uint64_t frame_count = 0;
static int got_damaged = 0;
static int resolution_check_thread_exited = 0;
static int xdamage_thread_exited = 0;
static int bomb_thread_exited = 0;

/*
	if wait damage before capture
*/
static int wait_vblank = 0;
static int cap_fail_cnt_max = 3;
static int bomb_timeout = 5 * 1000; // in ms
static int RES_CHECK_INTERVAL = 10; //ms

static gpointer
resolution_thread (void * inst)
{
    syslog(LOG_DEBUG, "[%s] enter", __func__);
    
    resolution_check_thread_exited = 0;

    XEvent ev;
	XDamageNotifyEvent *damage_ev;
	
    while (true) {
        do {
			
            XNextEvent (xdisp, &ev);
			
            if (ev.type == ConfigureNotify) {
                if (xwindow_w != -1u && xwindow_h != -1u
                    && xwindow_w != ev.xconfigurerequest.width && xwindow_h != ev.xconfigurerequest.height) {

                    XWindowAttributes attrs;
                    if (XGetWindowAttributes (xdisp, xwindow, &attrs)) {
                        if (xwindow_w != attrs.width && xwindow_h != attrs.height) {
                            syslog(LOG_WARNING, "resolution changed from %ux%u to %ux%u ? restart",
                                               xwindow_w, xwindow_h, attrs.width, attrs.height);
                            xwindow_w = attrs.width;
                            xwindow_h = attrs.height;
                            change_resolution_requested = true;
							exit(0);
                        } else {
                            syslog(LOG_DEBUG, "resolution not change");
                        }
                    }
                }
			}
			// if it is a xdamage event
			else if (ev.type == damage_event_base + XDamageNotify) { 
				damage_ev = (XDamageNotifyEvent *) (&ev);
				// empty or not
				if (damage_ev->level == XDamageReportNonEmpty) {
					syslog(LOG_DEBUG, "got a xdamage event");
					got_damaged = 1;
				}
			}
			
        } while (XPending (xdisp));
		
        usleep(RES_CHECK_INTERVAL * 1000);
    }
	
	resolution_check_thread_exited = 1;
	
    syslog(LOG_DEBUG, "[%s] exit", __func__);
}

static int BOMB_CHECK_INTERVAL = 50; //ms

static gpointer
bomb_thread (void * inst)
{
    syslog(LOG_DEBUG, "[%s] enter", __func__);
	
    bomb_thread_exited = 0;

    uint64_t lastCnt = frame_count;
    uint64_t changeTime = FrameLog::get_time();
    uint32_t err_cnt = 0;
    uint32_t b_restart = 0;
	
    while (true) {
	
		uint64_t nowTime = FrameLog::get_time();
		
		if (wait_vblank == 0) {
			// fixed fps
			if (streaming_requested
				&& nowTime - changeTime >= bomb_timeout * 1000  // 5s
				&& lastCnt == frame_count) {
				b_restart = 1;
			}
		}
		else {
			// capture only on desktop changing
			if (got_damaged) {
				if (nowTime - changeTime < 10 * BOMB_CHECK_INTERVAL * 1000) {
					err_cnt = 0;
					got_damaged = 0;
					// normal: a new frame has appeared since last damage event. 
				}
				else {
					err_cnt++;
					if (err_cnt > 20) {
						b_restart = 1;
					}
				}
			}
		}
			
		if (b_restart) {
			syslog(LOG_WARNING, "proccess blocked for %d ms ? restart", bomb_timeout);
			syslog(LOG_WARNING, "it has processed %llu frames", frame_count);
			exit(0);
		}
		
		if (lastCnt != frame_count) {
			changeTime = nowTime;
			lastCnt = frame_count;
		}
		
        usleep(BOMB_CHECK_INTERVAL * 1000); // 50 ms
    }

    bomb_thread_exited = 1;

    syslog(LOG_DEBUG, "[%s] exit", __func__);
}

static std::string
get_option(std::vector<ConcreteConfigureOption> options, const char* key)
{
    std::string ret;
    for (auto itr = options.begin(); itr != options.end(); itr++) {
        ConcreteConfigureOption opt = *itr;
        if (strncmp(opt.name, key, strlen(key)) == 0) {
            syslog(LOG_DEBUG, "[%s] %s = %s", __func__, key, opt.value);
            ret.assign(opt.value);
            break;
        }
    }
    return ret;
}

static int XDAMAGE_CHECK_INTERVAL = 50; //ms

/*
	check
*/
static gpointer
xdamage_thread (void* data)
{
	syslog(LOG_DEBUG, "[%s] enter", __func__);

	XEvent ev;
	XDamageNotifyEvent *damage_ev;
	xdamage_thread_exited = 0;

	// check if need to exit
 	while (true) {

		// if there are events in the queue 
		while (XPending (xdisp)) {

			// get next event
			XNextEvent (xdisp, &ev);

			// if it is a xdamage event
			if (ev.type == damage_event_base + XDamageNotify) { 
				damage_ev = (XDamageNotifyEvent *) (&ev);
				// empty or not
				if (damage_ev->level == XDamageReportNonEmpty) {
					syslog(LOG_DEBUG, "got a xdamage event");
					got_damaged = 1;
				}
			}
		}

		// if not sleep, it would consume much cpu time when there is no events coming
		usleep(XDAMAGE_CHECK_INTERVAL * 1000);
	
 	}

	xdamage_thread_exited = 1;
	
	syslog(LOG_DEBUG, "[%s] exit", __func__);
}


static bool
x11_task_init()
{
    change_resolution_requested = false;

    if (xdisp == NULL) {
        xdisp = XOpenDisplay (NULL); // to use input params
        if (!xdisp) {
            return false;
        }
    } else {
        syslog(LOG_DEBUG, "[%s] do nothing", __func__);
        return true;
    }
	
    Screen *screen = DefaultScreenOfDisplay (xdisp);
    guint xwidth = WidthOfScreen (screen);
    guint xheight = HeightOfScreen (screen);
    xwindow = RootWindowOfScreen (screen);
    XSelectInput(xdisp, xwindow, SubstructureNotifyMask);

	/* init resolution check */

    XWindowAttributes attrs;
    if (XGetWindowAttributes (xdisp, xwindow, &attrs)) {
        xwindow_w = attrs.width;
        xwindow_h = attrs.height;
        syslog(LOG_DEBUG, "[%s] resolution is %ux%u", __func__, xwindow_w, xwindow_h);
    }

	/* init xdamge */
	
	int error_base;
	damage = None;

	// check if xdamage extension is supported. if not, install it by 'apt-get install'
	if (XDamageQueryExtension (xdisp, &damage_event_base, &error_base)) {
		syslog(LOG_DEBUG, "[%s] damage_event_base %x, error_base %x", __func__, damage_event_base, error_base);
		// create a xdamage object.
		damage = XDamageCreate (xdisp, xwindow, XDamageReportNonEmpty);
		if (damage != None) {
			 damage_region = XFixesCreateRegion(xdisp, NULL, 0);
		     if (damage_region != None) {
				// the whole screen is where we instrested in
				XSelectInput(xdisp, xwindow, NoEventMask);	
		     }
		} 
		else {
			syslog(LOG_ERR, "Could not attach to XDamage");
			return false;
		}
	} 
	else {
		syslog(LOG_ERR, "X Server does not have XDamage extension");
		return false;
	}

	// start a thread to periodically check xdamage events
	resolution_thread_id = g_thread_new ("resolution-thread", (GThreadFunc) resolution_thread, 0);
	// xdamage_thread_id = g_thread_new ("xdamage-thread", (GThreadFunc) xdamage_thread, 0);
	
    return true;
}

/*
  0: not limit
  > 0: the absolute limit, if rssmem_limit == 5000, the process rss limit is 5000kB
  < 0: if "rssmem_limit == -2" and "current rss / initial rss >= 2" (-rssmem_limit), restart the process
*/
static int rssmem_limit = 0;

static void
memory_check()
{
    static int init_rss = 0;
    static const int delay_cnt = 5;
    static int counter = delay_cnt;

    FILE* fd;
    char line[1024] = {0};
    char virtual_file[64] = {0};
    char vmrss_name[32] = {0};
    int vmrss_num = 0;


    if (rssmem_limit == 0) {
        // mem limit is not enabled.
        return;
    }

    sprintf(virtual_file, "/proc/%d/status", getpid());
    fd = fopen(virtual_file, "r");
    if (!fd) {
        syslog(LOG_ERR, "open %s failed", virtual_file);
        return;
    }

    // 'VMRSS:' usually resides within the 100 top lines of the file
    for (int i = 0; i < 100; ++i) {
        memset(line, 0, sizeof(line));
        fgets(line, sizeof(line), fd);
        if (strcasestr(line, "VMRSS:") != NULL)
        {
            sscanf(line, "%s %d", vmrss_name, &vmrss_num);
            break;
        }
    }

    if (vmrss_num == 0) {
        syslog(LOG_ERR, "can not get rss");
        fclose(fd);
        return;
    }

    syslog(LOG_DEBUG, "rss (%d kB -> %d kB), rssmem_limit %d kB", init_rss, vmrss_num, rssmem_limit);

    if (init_rss == 0 || counter > 0) {
        // as mem usage is not stable immediately after process starting, use 'counter' to make a delay.
        init_rss = vmrss_num;
        --counter;
        if (counter == 0)
        {
            syslog(LOG_ERR, "use this init_rss %d kB", init_rss);
        }
    } else {
        if (rssmem_limit < 0) {
            if (vmrss_num / init_rss >= -rssmem_limit) {
                syslog(LOG_ERR, "possible memleak (%d kB -> %d kB), restart", init_rss, vmrss_num);
                quit_requested = true;
                init_rss = 0;
                counter = delay_cnt;
            }
        } else if (rssmem_limit > 0) {
            if (vmrss_num >= rssmem_limit) {
                syslog(LOG_ERR, "possible memleak (%d kB -> %d kB), restart", init_rss, vmrss_num);
                quit_requested = true;
                //change_resolution_requested = true;
                init_rss = 0;
                counter = delay_cnt;
            }
        } else {
            // not limit memory usage
        }
    }

    fclose(fd);
}

static void
do_capture(StreamPort &stream_port, FrameLog &frame_log, ConcreteAgent &agent)
{
	if (force_streaming != "") {

		streaming_requested = true;
		client_codecs.clear();
		client_codecs.insert(SPICE_VIDEO_CODEC_TYPE_H264);

		if (force_streaming.find("h265") != std::string::npos) {
			client_codecs.clear();
			client_codecs.insert(SPICE_VIDEO_CODEC_TYPE_H265);
		}
	}
	
    while (!quit_requested) {
        while (!quit_requested && !streaming_requested) {
			if (runs_in_vm) {
				read_command(stream_port, true);
			}
        }
        if (quit_requested) {
            return;
        }
        syslog(LOG_INFO, "streaming starts now");
        uint64_t time_last = 0;

        std::unique_ptr<FrameCapture> capture(agent.GetBestFrameCapture(client_codecs, plugin_name));
        if (!capture) {
            throw std::runtime_error("cannot find a suitable capture system");
        }

        std::vector<DeviceDisplayInfo> display_info;
        try {
            display_info = capture->get_device_display_info();
        } catch (const Error &e) {
            syslog(LOG_ERR, "Error while getting device display info: %s", e.what());
        }

        syslog(LOG_DEBUG, "Got device info of %zu devices from the plugin", display_info.size());
        for (const auto &info : display_info) {
            syslog(LOG_DEBUG, "   stream id %u: device address: %s, device display id: %u",
                   info.stream_id,
                   info.device_address.c_str(),
                   info.device_display_id);
        }

        if (display_info.size() > 0) {
            if (display_info.size() > 1) {
                syslog(LOG_WARNING, "Warning: the Frame Capture plugin returned device display "
                       "info for more than one display device, but we currently only support "
                       "a single device. Sending information for first device to the server.");
            }
			if (runs_in_vm) {
				stream_port.send<DeviceDisplayInfoMessage>(display_info[0]);
			}
        } else {
                 syslog(LOG_ERR, "Empty device display info from the plugin");
        }

        x11_task_init();
		
        uint64_t cap_fail_cnt = 0;
       	frame_count = 0;
        uint64_t time_start = FrameLog::get_time();
		uint64_t total_size = 0;
		int recent_size[10] = {0};
		uint64_t recent_time[10] = {0};
		int recent_num = 0;
		
        while (!quit_requested && streaming_requested && !change_resolution_requested) {

            // uint64_t time_before = FrameLog::get_time();

            FrameInfo frame = capture->CaptureFrame();
            if (frame.buffer == NULL || frame.buffer_size == 0) {
				if (cap_fail_cnt % 30 == 0) {
                	syslog(LOG_DEBUG, "timeout or screen not change");
				}
                ++cap_fail_cnt;
                if (wait_vblank == 0 && cap_fail_cnt >= cap_fail_cnt_max) { // 2s
                    change_resolution_requested = true;
                    syslog(LOG_DEBUG, "missing %u frames, trying restart", cap_fail_cnt);
                }

				// if not, proccess would not reponse to STOP message from client 
				if (runs_in_vm) {
					read_command(stream_port, false);
				}
				
                continue;
            } else {
                cap_fail_cnt = 0;
            }

			if (frame_count % 300 == 0) {
                memory_check();
            }

			uint64_t time_sent = FrameLog::get_time();

			// send first frame codec, size info
			if (frame.stream_start) {
                unsigned width, height;
                unsigned char codec;

                width = frame.size.width;
                height = frame.size.height;
                codec = capture->VideoCodecType();

                syslog(LOG_DEBUG, "wxh %ux%u  codec=%u", width, height, codec);
				if (runs_in_vm) {
					stream_port.send<FormatMessage>(width, height, codec);
				}
            }

			// send frames
            try {
				if (runs_in_vm) {
					stream_port.send<FrameMessage>(frame.buffer, frame.buffer_size);
				}
                if (dbg_fd) {
                    fwrite(frame.buffer, 1, frame.buffer_size, dbg_fd);
                }
            } catch (const WriteError& e) {
                utils::syslog(e);
                break;
            }
			
            uint64_t time_after = FrameLog::get_time();
			
			total_size += frame.buffer_size; // all size
			if (recent_num < 10) {
				recent_size[recent_num] = frame.buffer_size;
				recent_time[recent_num] = time_after;
				recent_num++;
			}
			else {
				// recent_num == 10
				memmove(recent_size, recent_size + 1, (recent_num - 1) * sizeof(int));
				recent_size[recent_num - 1] = frame.buffer_size;
				
				memmove(recent_time, recent_time + 1, (recent_num - 1) * sizeof(uint64_t));
				recent_time[recent_num - 1] = time_after;
			}

			//char* printMsg = new char[1024];
			//memset(printMsg, 0, 1024);

			frame.cap_ts = time_after - frame.cap_ts;
			/*if (plugin_name == "jmfbc" && (fpsi == 0 || frame_count % fpsi == 0)) {
		        syslog(LOG_DEBUG, "total time %llu us", frame.cap_ts);
		    }*/
						
            if (fpsi == 0 || frame_count % fpsi == 0) {
				
				uint64_t recent_total_size = 0;
				for (int i = 1; i < recent_num; ++i) {
					recent_total_size += recent_size[i];
				}
				uint64_t recent_total_time = recent_time[recent_num - 1] - recent_time[0];

                //snprintf(printMsg + strlen(printMsg), 1024 - strlen(printMsg),
                syslog(LOG_DEBUG,
                        "got a frame -- size %d bytes, loop %lu us, cap+enc+send %llu us, cap %llu us, enc %llu us, send %llu us, res %ux%u, fps %.3lf, bitrate %.3lf(%.3lf) kbps, sent %llu frames\n",
                        frame.buffer_size,
                        (time_after - time_last),
                        frame.cap_ts,
                        frame.cap_time,
                        frame.enc_ts,
                        time_after - time_sent,
                        frame.size.width, frame.size.height,
                        frame_count / ((time_after - time_start) / 1000000.0),
						total_size * 8 * 1000.0 / (double)(time_after - time_start),
						recent_total_size * 8 * 1000.0 / (double)recent_total_time,
                        frame_count);
            }
            time_last = time_after;

		    frame_count++;

			if (runs_in_vm) {
				read_command(stream_port, false);
			}
        }

		if (change_resolution_requested) {
			// if resolution changed, this process should restart as vaapi has some releasing faults.
				quit_requested = true;	
			syslog(LOG_DEBUG, "restart proccess as resolution changes");
		}

    }

    syslog(LOG_DEBUG, "exit do capture");
}

int main(int argc, char* argv[])
{
    const char *stream_port_name = "/dev/virtio-ports/org.spice-space.stream.0";
    int opt;
    const char *log_filename = NULL;
    bool log_binary = false;
    bool log_frames = false;
    const char *pluginsdir = PLUGINSDIR;

    enum {
        OPT_first = UCHAR_MAX,
        OPT_PLUGINS_DIR,
        OPT_LOG_BINARY,
        OPT_LOG_CATEGORIES,
    };
    static const struct option long_options[] = {
        { "plugins-dir", required_argument, NULL, OPT_PLUGINS_DIR},
        { "log-binary", no_argument, NULL, OPT_LOG_BINARY},
        { "log-categories", required_argument, NULL, OPT_LOG_CATEGORIES},
        { "help", no_argument, NULL, 'h'},
        { 0, 0, 0, 0}
    };
    std::vector<std::string> old_args(argv, argv+argc);

    openlog("jm-streaming-agent",
            isatty(fileno(stderr)) ? (LOG_PERROR|LOG_PID) : LOG_PID, LOG_USER);

	XInitThreads();
			
    setlogmask(LOG_UPTO(LOG_NOTICE));

    std::vector<ConcreteConfigureOption> options;
    while ((opt = getopt_long(argc, argv, "hp:c:l:d", long_options, NULL)) != -1) {
        switch (opt) {
        case 0:
            /* Handle long options if needed */
            break;
        case OPT_PLUGINS_DIR:
            pluginsdir = optarg;
            break;
        case 'p':
            stream_port_name = optarg;
            break;
        case 'c': {
            char *p = strchr(optarg, '=');
            if (p == NULL) {
                syslog(LOG_ERR, "Invalid '-c' argument value: %s", optarg);
                usage(argv[0]);
            }
            *p++ = '\0';
            options.push_back(ConcreteConfigureOption(optarg, p));
            break;
        }
        case OPT_LOG_BINARY:
            log_binary = true;
            break;
        case OPT_LOG_CATEGORIES:
            for (const char *tok = strtok(optarg, ":"); tok; tok = strtok(nullptr, ":")) {
                std::string cat = tok;
                if (cat == "frames") {
                    log_frames = true;
                }
                // ignore not existing, compatibility for future
            }
            break;
        case 'l':
            log_filename = optarg;
            break;
        case 'd':
            setlogmask(LOG_UPTO(LOG_DEBUG));
            break;
        case 'h':
            usage(argv[0]);
            break;
        }
    }

    register_interrupts();

   // use x11 to track resolution changing events
	std::string optvalue = get_option(options, "capture-display");
	if (optvalue != "") {
		xdisp_name = optvalue;
	}
	optvalue = get_option(options, "rss-limit");
	if (optvalue != "") {
		rssmem_limit = atoi(optvalue.c_str());
	}
	optvalue = get_option(options, "usevblank");
	if (optvalue != "") {
		wait_vblank = atoi(optvalue.c_str());
	}
	optvalue = get_option(options, "fpsi");
	if (optvalue != "") {
		fpsi = atoi(optvalue.c_str());
	}
	optvalue = get_option(options, "cap-fail-cnt");
	if (optvalue != "") {
		cap_fail_cnt_max = atoi(optvalue.c_str());
	}
	optvalue = get_option(options, "bomb-timeout");
	if (optvalue != "") {
		bomb_timeout = atoi(optvalue.c_str());
	}
	optvalue = get_option(options, "codec");
	if (optvalue != "") {
		codec_type = atoi(optvalue.c_str());
	}
	optvalue = get_option(options, "screen-id");
	if (optvalue != "") {
		screen_id = atoi(optvalue.c_str());
	}
	std::string debug_output = get_option(options, "debug-output");
	if (debug_output != "") {
		
		char buffer[32] = {0};
		sprintf(buffer, "%d", screen_id);
		debug_output.append("/jm-");
		debug_output.append(buffer);
		debug_output.append("-");
		
		int findex = 0;
		int earliest_index = 0;
		time_t earliest_time = 0;
		std::string test_fname;
		
		while (findex < 5) {
			
			test_fname = debug_output;
			memset(buffer, 0, sizeof(buffer));
			sprintf(buffer, "%d", findex);
			test_fname.append(buffer);
		
			if (codec_type == 0) {
				test_fname.append(".264");
			}
			else if (codec_type == 1) {
				test_fname.append(".hevc");
			}
			syslog(LOG_DEBUG, "trying output to %s", test_fname.c_str());
			
			if (access(test_fname.c_str(), F_OK) == -1) {
				break;
			}
			
			struct stat file_stat;
			stat(test_fname.c_str(), &file_stat);
			
			if (earliest_time == 0 || file_stat.st_mtime < earliest_time) {
				earliest_time = file_stat.st_mtime;
				earliest_index = findex;
			}
			
			findex++;
		}
		
		if (findex >= 5) {
			test_fname = debug_output;
			memset(buffer, 0, sizeof(buffer));
			sprintf(buffer, "%d", earliest_index);
			test_fname.append(buffer);
		
			if (codec_type == 0) {
				test_fname.append(".264");
			}
			else if (codec_type == 1) {
				test_fname.append(".hevc");
			}
		}
		
		syslog(LOG_DEBUG, "output to %s", test_fname.c_str());
		
		dbg_fd = fopen(test_fname.c_str(), "wb+");
	}

	plugin_name = get_option(options, "plugin-name");
	force_streaming = get_option(options, "force-streaming");
	optvalue = get_option(options, "vm");
	if (optvalue != "") {
		runs_in_vm = atoi(optvalue.c_str());
		if (runs_in_vm == 0 && force_streaming == "") {
			force_streaming.assign("h264");
			// runs in physical machine
		}
	}
	
	optvalue = get_option(options, "debug-wait");
	if (optvalue != "") {
		int dbg_wait = atoi(optvalue.c_str());
		if (dbg_wait > 0) {
			while (dbg_wait > 0) {
				sleep(1);
				printf("debug wait %d\n", dbg_wait);
				dbg_wait--;
			}
		}
	}
	
    CursorUpdater* running_cursor_thread = NULL;

    try {
        FrameLog frame_log(log_filename, log_binary, log_frames);

        ConcreteAgent agent(options, &frame_log);

        // register built-in plugins
        MjpegPlugin::Register(&agent);

        agent.LoadPlugins(pluginsdir);

        for (const std::string& arg: old_args) {
            frame_log.log_stat("Args: %s", arg.c_str());
        }
        old_args.clear();
		
		StreamPort stream_port(stream_port_name);
		if (runs_in_vm) {
		    running_cursor_thread = new CursorUpdater(&stream_port); // can live only while stream_port is alive !
		}
		
		bomb_thread_id = g_thread_new ("bomb_check", (GThreadFunc) bomb_thread, 0);
		
        do_capture(stream_port, frame_log, agent);

        if (runs_in_vm) {
            delete running_cursor_thread;
        }
    }
    catch (std::exception &err) {
        syslog(LOG_ERR, "%s", err.what());
        return EXIT_FAILURE;
    }

    syslog(LOG_INFO, "exit main()");
    
    return EXIT_SUCCESS;
}
